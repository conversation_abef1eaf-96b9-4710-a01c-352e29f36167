import React, {useCallback, useState} from 'react';
import {Platform, ScrollView, StyleSheet, Alert, Modal, TextInput} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {Feather, FontAwesome6, AntDesign} from '@expo/vector-icons';
import {Block, Button, Image, Text} from '../components/';
import {useData, useTheme, useTranslation} from '../hooks/';
import {useAuth} from '../context/AuthContext';
import useAxiosPrivate from '../lib/use-axios-private';
import Constants from 'expo-constants';
const isAndroid = Platform.OS === 'android';

const NewProfilePage = () => {
  const authData = useAuth();
  const userData = authData?.userData;
  const {t} = useTranslation();
  const navigation = useNavigation();
  const {assets, colors, gradients, sizes} = useTheme();
  const isTherapist = userData?.role === 'therapist';
  const axiosPrivate = useAxiosPrivate();
  
  // State for account deletion
  const [isDeleting, setIsDeleting] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [emailInput, setEmailInput] = useState('');
  const [emailError, setEmailError] = useState('');
  
  const logout = useCallback(async () => {
    authData?.logout();
    navigation.navigate('Login' as never);
  }, [navigation, authData]);
  
  const handleOpenDeleteModal = useCallback(() => {
    setEmailInput('');
    setEmailError('');
    setModalVisible(true);
  }, []);
  
  const handleCancelDelete = useCallback(() => {
    setModalVisible(false);
  }, []);
  
  const confirmDeleteAccount = useCallback(async () => {
    // Verify email matches the user's email
    if (!emailInput || emailInput !== userData?.email) {
      setEmailError('The email address you entered does not match your account.');
      return;
    }
    
    try {
      setIsDeleting(true);
      setModalVisible(false);
      
      // Call the API endpoint using axiosPrivate
      const response = await axiosPrivate.post('/auth/deactivate/');
      
      // Account deleted successfully
      Alert.alert(
        'Account Deleted',
        'Your account has been successfully deleted.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Logout and redirect to login screen
              authData?.logout();
              navigation.navigate('Login' as never);
            },
          },
        ],
      );
    } catch (error) {
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again later.',
      );
      console.error('Delete account error:', error);
    } finally {
      setIsDeleting(false);
    }
  }, [emailInput, userData, authData, navigation, axiosPrivate]);
  
  return (
    <Block
      safe
      style={{
        marginTop:
          Platform.OS === 'android' ? Constants.statusBarHeight + 30 : 30,
        backgroundColor: 'transparent',
      }}
      marginBottom={120}>
      
      {/* Delete Account Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={handleCancelDelete}>
        <Block 
          flex={1} 
          justify="center" 
          align="center"
          color="rgba(0, 0, 0, 0.5)">
          <Block 
            width="85%" 
            radius={10} 
            padding={sizes.sm}
            color={colors.white}
            style={{maxHeight: 320}}>
            <Text h5 semibold marginBottom={sizes.s}>
              Delete Account
            </Text>
            <Text p marginBottom={sizes.sm}>
              To confirm deletion, please enter your email address:
            </Text>
            <TextInput
              style={{
                borderWidth: 1,
                borderColor: emailError ? colors.danger : colors.gray,
                borderRadius: 8,
                padding: 10,
                marginBottom: emailError ? 4 : sizes.sm,
              }}
              placeholder="Email address"
              value={emailInput}
              onChangeText={(text) => setEmailInput(text)}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            {emailError ? (
              <Text p color={colors.danger} marginBottom={sizes.sm}>
                {emailError}
              </Text>
            ) : null}
            <Block row justify="flex-end">
              <Button
                gray
                width={80}
                height={40}
                marginRight={sizes.s}
                onPress={handleCancelDelete}>
                <Text p bold>
                  Cancel
                </Text>
              </Button>
              <Button
                danger
                width={80}
                height={40}
                disabled={isDeleting}
                onPress={confirmDeleteAccount}>
                <Text p bold white>
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </Text>
              </Button>
            </Block>
          </Block>
        </Block>
      </Modal>
      
      <ScrollView showsVerticalScrollIndicator={false}>
        <Block paddingHorizontal={sizes.text}>
          {/* Profile */}
          <Block
            card
            paddingVertical={sizes.padding}
            marginBottom={sizes.m}
            gradient={['#cb964a', '#966419']}>
            <Block flex={0} align="center" marginBottom={sizes.m}>
              <Block flex={1} row>
                <Block
                  radius={35}
                  width={70}
                  height={70}
                  color={colors.white}
                  align="center"
                  flex={0}
                  justify="center">
                  <Text h3 color={colors.primary} bold>
                    {`${userData?.first_name.charAt(0)}${userData?.last_name.charAt(0)}`}
                  </Text>
                </Block>
              </Block>
              <Text h4 center white marginTop={sizes.base}>
                {`${userData?.first_name} ${userData?.last_name}`}
              </Text>
              <Text
                transform="capitalize"
                p
                center
                white
                marginTop={2}
                marginBottom={0}>
                {`${userData?.role}`}
              </Text>
              {/* <Block
                row
                align="center"
                width={200}
                justify="center"
                marginTop={sizes.xs}>
                <Block row align="center" justify="center">
                  <FontAwesome6
                    name="ranking-star"
                    size={16}
                    color={colors.white}
                    style={{marginRight: 5}}
                  />
                  <Text p bold white>
                    Premium
                  </Text>
                </Block>
                <Block row align="center" justify="center">
                  <Feather
                    name="award"
                    size={16}
                    color={colors.white}
                    style={{marginRight: sizes.xs}}
                  />
                  <Text p white bold>
                    {'50'.toLocaleString()} pts
                  </Text>
                </Block>
              </Block> */}
            </Block>
          </Block>

          {/* Stats */}
          {!isTherapist && false && (
            <Block
              card
              margin={sizes.sm}
              marginTop={-sizes.xl}
              style={styles.statsCard}>
              <Block row justify="space-around">
                <Block align="center">
                  <Block style={styles.statIconContainer}>
                    <Feather name="calendar" size={20} color={colors.primary} />
                  </Block>
                  <Text
                    p
                    semibold
                    color={colors.primary}
                    marginTop={sizes.base}>
                    10
                  </Text>
                  {/* <Text p size={13}>
                  Appointments
                </Text> */}
                </Block>

                <Block align="center">
                  <Block style={styles.statIconContainer}>
                    <FontAwesome6
                      name="ranking-star"
                      size={18}
                      color={colors.primary}
                    />
                  </Block>
                  <Text
                    p
                    semibold
                    color={colors.primary}
                    marginTop={sizes.base}>
                    Premium
                  </Text>
                  {/* <Text p size={13}>
                  Rank
                </Text> */}
                </Block>

                <Block align="center">
                  <Block style={styles.statIconContainer}>
                    <Feather name="award" size={18} color={colors.primary} />
                  </Block>
                  <Text
                    p
                    semibold
                    color={colors.primary}
                    marginTop={sizes.base}>
                    5,000
                  </Text>
                </Block>
              </Block>
            </Block>
          )}

          <Block marginTop={sizes.sm}>
            {/* Additional Info */}
            <Block card padding={sizes.m} marginBottom={sizes.m}>
              <Text h5 semibold marginBottom={sizes.sm}>
                Contact Information
              </Text>
              <Block row align="center" marginBottom={sizes.sm}>
                <Feather
                  name="mail"
                  size={20}
                  color={colors.primary}
                  style={{marginRight: sizes.s}}
                />
                <Text p>{userData?.email}</Text>
              </Block>
              <Block row align="center">
                <Feather
                  name="phone"
                  size={20}
                  color={colors.primary}
                  style={{marginRight: sizes.s}}
                />
                <Text p>{userData?.phone_number}</Text>
              </Block>
            </Block>
          </Block>

          {/* settings */}
          {/* <Block
            card
            paddingHorizontal={sizes.sm}
            marginBottom={sizes.sm}
            marginTop={sizes.xs}
            justify="space-between"
            paddingRight={-7}
            row>
            <Block row flex={1} align="center">
              <Block
                flex={0}
                align="center"
                justify="center"
                radius={sizes.s}
                width={sizes.md}
                height={sizes.md}
                marginRight={sizes.s}
                gradient={gradients.primary}>
                <Image
                  source={assets?.settings}
                  color={colors.white}
                  radius={0}
                />
              </Block>
              <Block>
                <Text semibold>{t('settings.recommended.title')}</Text>
                <Text size={12}>{t('settings.recommended.subtitle')}</Text>
              </Block>
            </Block>

            <Button padding={0} onPress={() => navigation.navigate('Settings')}>
              <Image
                source={assets.arrow}
                color={colors.icon}
                radius={0}
                height={18}
                width={10}
              />
            </Button>
          </Block> */}

          {/* Quick Actions */}
          <Block
            row
            justify="center"
            marginVertical={sizes.m}
            paddingHorizontal={sizes.s}>
            {!isTherapist && (
              <Button
                primary
                marginRight={sizes.sm}
                flex={1.2}
                row
                align="center"
                justify="center"
                onPress={() => navigation.navigate('BookingHistory')}>
                <Feather name="calendar" size={20} color={colors.white} />
                <Text white marginLeft={sizes.s}>
                  Recent Bookings
                </Text>
              </Button>
            )}
            {/* <Button
              gradient={gradients.secondary}
              flex={0.8}
              row
              align="center"
              justify="center">
              <Feather name="message-square" size={20} color={colors.white} />
              <Text white marginLeft={sizes.s}>
                Contact Us
              </Text>
            </Button> */}
          </Block>

          {/* logout */}
          <Block marginVertical={sizes.m} paddingHorizontal={sizes.s}>
            <Button
              danger
              // gradient={gradients.primary}
              marginRight={sizes.sm}
              marginBottom={sizes.sm}
              flex={1}
              width={'100%'}
              row
              align="center"
              justify="center"
              onPress={logout}>
              <AntDesign name="logout" size={20} color={colors.white} />
              <Text white marginLeft={sizes.s}>
                Logout
              </Text>
            </Button>
          </Block>
          
          {/* Delete Account Link */}
          <Block align="center" marginBottom={sizes.xl}>
            <Button
              noBorder
              transparent
              onPress={handleOpenDeleteModal}
              disabled={isDeleting}>
              <Text
                p
                size={14}
                color={colors.danger}
                semibold>
                {isDeleting ? 'Deleting...' : 'Delete my account'}
              </Text>
            </Button>
          </Block>
        </Block>
      </ScrollView>
    </Block>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statsCard: {
    marginTop: -30,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  statIconContainer: {
    width: 38,
    height: 38,
    borderRadius: 19,
    backgroundColor: 'rgba(100,100,100,0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    // marginBottom: 8,
  },
  actionButton: {
    height: 50,
    borderRadius: 25,
  },
  settingsButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 5,
  },
  settingsIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(100,100,100,0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
});
export default NewProfilePage;