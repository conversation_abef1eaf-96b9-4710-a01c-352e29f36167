import React from 'react';
import {ScrollView, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {Feather, FontAwesome6} from '@expo/vector-icons';

import {Block, Button, Image, Text} from '../components/';
import {useData, useTheme, useTranslation} from '../hooks/';

const ImprovedProfilePage = () => {
  const {user} = useData();
  const {t} = useTranslation();
  const navigation = useNavigation();
  const {assets, colors, gradients, sizes} = useTheme();

  const stats = [
    {icon: 'calendar', label: 'Appointments', value: '12'},
    {icon: 'star', label: 'Rating', value: '4.9'},
    {icon: 'award', label: 'Points', value: '50'},
  ];

  return (
    <Block safe>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Block>
          {/* Header */}
          <Block gradient={gradients.primary} style={styles.header}>
            <Block
              flex={0}
              align="center"
              justify="center"
              style={styles.avatarContainer}>
              <Block style={styles.avatar}>
                <Text h2 white bold>
                  {user?.name?.charAt(0) || 'M'}
                </Text>
              </Block>
            </Block>
            <Text h4 center white marginTop={sizes.sm}>
              {user?.name || 'Michael Peter'}
            </Text>
            <Block row align="center" justify="center" marginTop={sizes.sm}>
              <FontAwesome6
                name="ranking-star"
                size={16}
                color={colors.white}
                style={{marginRight: 5}}
              />
              <Text p semibold white>
                Premium
              </Text>
            </Block>
          </Block>

          {/* Stats */}
          <Block card margin={sizes.sm} style={styles.statsCard}>
            <Block row justify="space-around">
              {stats.map((stat, index) => (
                <Block key={`stat-${index}`} align="center">
                  <Block style={styles.statIconContainer}>
                    <Feather
                      name={stat.icon as keyof typeof Feather.glyphMap}
                      size={24}
                      color={colors.primary}
                    />
                  </Block>
                  <Text p bold color={colors.primary} marginTop={sizes.s}>
                    {stat.value}
                  </Text>
                  <Text size={sizes.text}>{stat.label}</Text>
                </Block>
              ))}
            </Block>
          </Block>

          <Block padding={sizes.sm}>
            {/* Additional Info */}
            <Block card padding={sizes.m} marginBottom={sizes.m}>
              <Text h5 semibold marginBottom={sizes.sm}>
                Contact Information
              </Text>
              <Block row align="center" marginBottom={sizes.sm}>
                <Feather
                  name="mail"
                  size={20}
                  color={colors.primary}
                  style={{marginRight: sizes.s}}
                />
                <Text p><EMAIL></Text>
              </Block>
              <Block row align="center">
                <Feather
                  name="phone"
                  size={20}
                  color={colors.primary}
                  style={{marginRight: sizes.s}}
                />
                <Text p>08036314163</Text>
              </Block>
            </Block>

            {/* Quick Actions */}
            <Block row justify="space-between" marginBottom={sizes.m}>
              <Button
                gradient={gradients.primary}
                marginRight={sizes.s}
                flex={1}
                row
                align="center"
                justify="center"
                style={styles.actionButton}>
                <Feather name="calendar" size={20} color={colors.white} />
                <Text white marginLeft={sizes.s}>
                  Book Appointment
                </Text>
              </Button>
              <Button
                gradient={gradients.secondary}
                flex={1}
                row
                align="center"
                justify="center"
                style={styles.actionButton}>
                <Feather name="message-square" size={20} color={colors.white} />
                <Text white marginLeft={sizes.s}>
                  Contact Us
                </Text>
              </Button>
            </Block>

            {/* Settings */}
            <Button
              row
              align="center"
              justify="space-between"
              onPress={() => navigation.navigate('Settings')}
              style={styles.settingsButton}>
              <Block row align="center">
                <Block style={styles.settingsIconContainer}>
                  <Image
                    source={assets?.settings}
                    color={colors.white}
                    radius={0}
                  />
                </Block>
                <Block>
                  <Text semibold>{t('settings.recommended.title')}</Text>
                  <Text size={12}>{t('settings.recommended.subtitle')}</Text>
                </Block>
              </Block>
              <Feather name="chevron-right" size={24} color={colors.icon} />
            </Button>
          </Block>
        </Block>
      </ScrollView>
    </Block>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statsCard: {
    marginTop: -30,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(100,100,100,0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  actionButton: {
    height: 50,
    borderRadius: 25,
  },
  settingsButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 5,
  },
  settingsIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(100,100,100,0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
});

export default ImprovedProfilePage;
