import React, {useMemo, useState} from 'react';
import {ActivityIndicator, StyleSheet, View} from 'react-native';
import {Block, Text, Button} from '../../components/';
import {useTheme} from '../../hooks/';
import {Feather} from '@expo/vector-icons';
import CancellationPolicy from './CancellationPolicy';
import {useBooking} from '../../context/BookingContext';
// import {axiosPrivate} from '../../lib/use-axios-private';
import Toast from 'react-native-toast-message';
import {IUserData, useAuth} from '../../context/AuthContext';
import useAxiosPrivate from '../../lib/use-axios-private';
import * as SecureStore from 'expo-secure-store';
import {ServiceValue} from './types';
interface BookingSummaryProps {
  handleNext: () => void;
}

interface BookingPayload {
  services: ServiceValue[];
  serviceType?: string | null;
  therapist_id?: number | string | null;
  date?: string | null;
  time?: string | null;
  duration?: number | null;
  total_price?: number | null;
  location?: string | null;
  total_duration?: number | null;
  // Optional fields based on service type
  user_package_id?: number | string | null;
  shared_package_id?: number | string | null;
  unlimited_package_id?: number | string | null;
  package_balance_id?: number | string | null;
  package_id?: number | string | null;
}

const studios = {'Studio A Lounge': 'A', 'Studio B Lounge': 'B'};
const BookingSummary: React.FC<BookingSummaryProps> = ({handleNext}) => {
  const axiosPrivate = useAxiosPrivate();
  const {bookingData} = useBooking();
  const {sizes, colors} = useTheme();
  const authData = useAuth();
  const [agreeTerms, setAgreeTerms] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  
  const isUnlimitedPackage = bookingData.package?.type === 'my_unlimited_package' || 
                             bookingData.package?.type === 'unlimited_package';

  const totalPrice = useMemo(() => {
    if (bookingData.package) {
      return bookingData.package.price;
    }
    return bookingData.services
      ? bookingData.services.reduce(
          (
            total: number,
            service: {id: number; name: string; time: number; price: number},
          ) => total + service.price,
          0,
        )
      : 0;
  }, [bookingData.package, bookingData.services]);

  const totalDuration = useMemo(
    () =>
      bookingData.package
        ? bookingData.package?.time
        : bookingData.services?.reduce((acc, item) => acc + item.time, 0),
    [bookingData.package, bookingData.services],
  );
  
  const renderServiceItem = (item: {
    id: number;
    name: string;
    time: number;
    price: number;
    type?: string;
  }) => {
    // Check if this is an unlimited package
    const isItemUnlimited = item.type === 'unlimited_package' || 
                            item.type === 'my_unlimited_package';
    
    return (
      <Block row justify="space-between" marginBottom={sizes.xs} key={item.id}>
        <Block row>
          <Text p>{item.name}</Text>
          {!isItemUnlimited && (
            <Text p size={12} marginLeft={sizes.xs}>
              ({item.time} mins)
            </Text>
          )}
        </Block>
        <Text p semibold>
          {item.price} AED
        </Text>
      </Block>
    );
  };

  const renderSummaryItem = (icon: string, title: string, value: string) => (
    <Block row align="center" marginBottom={sizes.s}>
      <Feather
        name={icon as keyof typeof Feather.glyphMap}
        size={20}
        color={colors.primary}
        style={styles.icon}
      />
      <Block>
        <Text p semibold>
          {title}
        </Text>
        <Text p color={colors.gray}>
          {value}
        </Text>
      </Block>
    </Block>
  );

  const isPackageSelected = useMemo(() => {
    return !!bookingData?.package;
  }, [bookingData.package]);

  const handleConfirmBooking = async () => {
    if (!agreeTerms) return;

    // Create services array with proper type handling
    let services: ServiceValue[] = [];

    if (isPackageSelected && bookingData.package) {
      services = [
        {
          id: Number(bookingData.package.durationId) || 0, // Convert to number or use 0 as fallback
          name: bookingData.package.name || '',
          time: Number(bookingData.package.time) || 0,
          price: Number(bookingData.package.price) || 0,
          type: bookingData.package.type || '',
        },
      ];
    } else if (bookingData.services && bookingData.services.length > 0) {
      services = bookingData.services.map((service) => ({
        id: Number(service.durationId) || 0,
        name: service.name || '',
        time: Number(service.time) || 0,
        price: Number(service.price) || 0,
      }));
    }

    // Extract serviceType with proper fallback
    const serviceType = bookingData.package?.type || '';

    // Create base payload with proper type handling
    const payload: BookingPayload = {
      services,
      serviceType,
      therapist_id: bookingData.therapist?.id || 0,
      date: bookingData.date || '', // Convert null to empty string
      time: bookingData.time || '', // Convert null to empty string
      duration: isPackageSelected
        ? Number(bookingData.allocatedMinutes) || 0
        : undefined,
      total_price: totalPrice,
      location: bookingData?.location?.location || '',
      total_duration:
        Number(bookingData.allocatedMinutes) || totalDuration || 0,
    };

    // Add appropriate package IDs based on the service type
    if (serviceType === 'mypackage') {
      payload.user_package_id = bookingData.package?.id || 0;
    } else if (['my_shared_package'].includes(serviceType)) {
      payload.shared_package_id = bookingData.package?.id || 0;
      payload.serviceType = 'shared_package';
    } else if (['my_unlimited_package'].includes(serviceType)) {
      payload.unlimited_package_id = bookingData.package?.id || 0;
      payload.serviceType = 'unlimited_package';
    } else if (serviceType === 'package') {
      // For new package purchases - avoid null by using undefined
      payload.package_balance_id = bookingData.package?.durationId || undefined;
      payload.package_id = bookingData.package?.durationId || undefined;
    }

    setIsLoading(true);

    try {
      let response;

      // Determine which endpoint to use based on service type
      if (
        !serviceType ||
        ['service', 'package', 'mypackage'].includes(serviceType)
      ) {
        // Regular services and packages endpoint
        response = await axiosPrivate.post(
          `/appointment/appointments/`,
          payload,
        );
      } else if (
        ['shared_package', 'my_shared_package'].includes(serviceType)
      ) {
        // Shared package endpoint
        response = await axiosPrivate.post(
          `/appointment/appointments/shared/`,
          payload,
        );
      } else if (
        ['unlimited_package', 'my_unlimited_package'].includes(serviceType)
      ) {
        // Unlimited package endpoint
        response = await axiosPrivate.post(
          `/appointment/appointments/unlimited/`,
          payload,
        );
      }

      Toast.show({
        type: 'success',
        text1: 'Booking Successful!!!',
      });
      handleNext();
    } catch (error) {
      console.error('Error creating appointment:', error);

      Toast.show({
        type: 'error',
        text1: 'Failed to create the appointment. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Block>
      <Text h5 semibold marginBottom={sizes.m}>
        Booking Summary
      </Text>
      <Block card padding={sizes.m} marginBottom={sizes.m}>
        <Text h5 semibold marginBottom={sizes.s}>
          Services:
        </Text>
        {bookingData.package
          ? renderServiceItem(bookingData.package)
          : bookingData.services && bookingData.services.map(renderServiceItem)}

        {/* Display allocated minutes if package is selected */}
        {bookingData.package && bookingData.allocatedMinutes && (
          <Block row marginBottom={sizes.s}>
            <Text p color={colors.gray}>Allocated time: </Text>
            <Text p semibold>{bookingData.allocatedMinutes} minutes</Text>
          </Block>
        )}

        {renderSummaryItem(
          'map-pin',
          'Location',
          bookingData.location?.name || 'Not selected',
        )}
        {renderSummaryItem(
          'user',
          'Therapist',
          bookingData.therapist?.name || 'Not selected',
        )}
        {renderSummaryItem(
          'calendar',
          'Date and Time',
          bookingData.date && bookingData.time
            ? `${bookingData.date} at ${bookingData.time}`
            : 'Not selected',
        )}

        <Block
          row
          justify="space-between"
          marginTop={sizes.m}
          paddingTop={sizes.s}
          style={styles.totalContainer}>
          <Text h5 semibold>
            Total
          </Text>
          <Text h5 semibold color={colors.primary}>
            {totalPrice} AED
          </Text>
        </Block>
      </Block>

      <CancellationPolicy
        agreed={agreeTerms}
        onAgree={(agree) => setAgreeTerms(agree)}
      />

      <Button
        flex={1}
        onPress={handleConfirmBooking}
        style={styles.confirmButton}
        disabled={!agreeTerms}
        color={!agreeTerms ? colors.gray : colors.primary}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#fff" />
          </View>
        ) : (
          <Text white opacity={!agreeTerms ? 0.5 : 1} bold>
            Confirm Booking
          </Text>
        )}
      </Button>
    </Block>
  );
};

const styles = StyleSheet.create({
  icon: {
    marginRight: 10,
  },
  totalContainer: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  confirmButton: {
    height: 50,
    borderRadius: 25,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
    backgroundColor: 'transparent',
  },
});

export default BookingSummary;