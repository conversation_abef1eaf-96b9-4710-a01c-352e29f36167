import React, {useState, useEffect, useMemo} from 'react';
import {ActivityIndicator, StyleSheet} from 'react-native';
import {Calendar, DateData} from 'react-native-calendars';
import {Block, Text, Button} from '../../components/';
import {useTheme} from '../../hooks/';
import {BookingData} from './types';
import {Feather} from '@expo/vector-icons';
import {useBooking} from '../../context/BookingContext';
import useGetAllocatedTime from '../../hooks/useGetAllocatedTime';
import CustomSlider from '../../components/CustomSlider';
import TimeSelectionModal from './TimeSelectionModal';

interface SelectDateTimeProps {
  handleNext: () => void;
}

const SelectDateTime: React.FC<SelectDateTimeProps> = ({handleNext}) => {
  const {bookingData, updateBookingData} = useBooking();
  const {sizes, colors} = useTheme();
  const [selectedDate, setSelectedDate] = useState<string | null>(null);

  const {timeChoices, allocatedTimes, isLoading} = useGetAllocatedTime();

  const handleDateSelect = (date: DateData) => {
    setSelectedDate(date.dateString);
    updateBookingData('date', date.dateString);
    updateBookingData('time', null);
  };

  const handleTimeSelect = (time: string) => {
    updateBookingData('time', time);
  };

  const handleAllocatedMinutesSelect = (time: string) => {
    updateBookingData('allocatedMinutes', time);
  };

  // Determine the maximum allowed allocated time
  const maxAllowedTime = useMemo(() => {
    if (bookingData?.package) {
      // If the package has a remaining_time property, use that as the maximum
      return bookingData.package.time || allocatedTimes;
    }
    return allocatedTimes;
  }, [bookingData?.package, allocatedTimes]);


  const isDisabled = useMemo(() => {
    if (!bookingData?.date) return false;

    const hasServices = bookingData?.services && !!bookingData?.services.length;
    const hasPackage = !!bookingData?.package;
    const hasTime = !!bookingData?.time;
    const hasAllocatedMinutes = !!bookingData?.allocatedMinutes;

    return (
      (hasServices && hasTime) || (hasPackage && hasTime && hasAllocatedMinutes)
    );
  }, [
    bookingData?.date,
    bookingData?.services,
    bookingData?.package,
    bookingData?.time,
    bookingData?.allocatedMinutes,
  ]);
  return (
    <Block>
      <Text h5 semibold marginBottom={sizes.m}>
        Select Date and Time
      </Text>
      <Calendar
        onDayPress={handleDateSelect}
        minDate={new Date().toISOString().split('T')[0]}
        markedDates={{
          ...(selectedDate
            ? {
                [selectedDate]: {
                  selected: true,
                  selectedColor: colors.primary as string,
                },
              }
            : {}),
        }}
        theme={{
          backgroundColor: colors.card as string,
          calendarBackground: colors.card as string,
          textSectionTitleColor: colors.text as string,
          selectedDayBackgroundColor: colors.primary as string,
          selectedDayTextColor: colors.white as string,
          todayTextColor: colors.primary as string,
          dayTextColor: colors.text as string,
          textDisabledColor: colors.gray as string,
          arrowColor: colors.primary as string,
          monthTextColor: colors.text as string,
          textMonthFontWeight: 'bold',
          textDayFontSize: 16,
          textMonthFontSize: 18,
          textDayHeaderFontSize: 14,
        }}
        style={styles.calendar}
      />

      <Block>
        {bookingData.date && isLoading ? (
          // Loading Indicator
          <Block align="center" justify="center" marginTop={sizes.l}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text marginTop={sizes.s} p>
              Loading available times...
            </Text>
          </Block>
        ) : bookingData.package ? (
          Object.keys(timeChoices).length > 0 && (
            <Block marginTop={sizes.m}>
              <Text h5 semibold marginBottom={sizes.s}>
                Available Times
              </Text>
              <TimeSelectionModal handleTimeSelect={handleTimeSelect} />
            </Block>
          )
        ) : (
          timeChoices && (
            <Block marginTop={sizes.m}>
              <Text h5 semibold marginBottom={sizes.s}>
                Available Times
              </Text>
              <Block row wrap="wrap" justify="space-between">
                {Array.isArray(timeChoices) &&
                  timeChoices.map((time) => (
                    <Button
                      key={time}
                      onPress={() => handleTimeSelect(time)}
                      color={
                        bookingData.time === time ? colors.primary : colors.card
                      }
                      marginBottom={sizes.s}
                      padding={sizes.s}
                      width={(sizes.width - sizes.padding * 3) / 3}
                      style={styles.timeButton}>
                      <Feather
                        name="clock"
                        size={16}
                        color={
                          bookingData.time === time ? colors.white : colors.text
                        }
                        style={styles.clockIcon}
                      />
                      <Text
                        p
                        semibold
                        center
                        color={
                          bookingData.time === time ? colors.white : colors.text
                        }>
                        {time}
                      </Text>
                    </Button>
                  ))}
              </Block>
            </Block>
          )
        )}
      </Block>

      {/* if a package is selected. show the allocated time section */}

      <Block>
        {bookingData?.package && bookingData?.time && allocatedTimes && (
          <Block marginTop={sizes.m}>
            {/* Display package remaining time - only for non-unlimited packages */}
            <Block marginBottom={sizes.s}>
              <Text h5 semibold marginBottom={sizes.xs}>
                Package Time Allocation
              </Text>
              {bookingData.package.type === 'my_unlimited_package' || bookingData.package.type === 'unlimited_package' ? (
                <Text p color={colors.gray}>
                  Select how much time to allocate for this appointment:
                </Text>
              ) : (
                <Text p color={colors.gray}>
                  Your package has <Text p semibold>{bookingData.package.time}</Text> minutes remaining. Select how much time to allocate for this appointment:
                </Text>
              )}
            </Block>

            <CustomSlider
              maxValue={
                bookingData.package.type === 'my_unlimited_package' || bookingData.package.type === 'unlimited_package'
                  ? allocatedTimes as number
                  : Math.min(maxAllowedTime as number, bookingData.package.time)
              }
              onValueChange={(time) => handleAllocatedMinutesSelect(`${time}`)}
            />
          </Block>
        )}
      </Block>

      <Block marginTop={sizes.md}>
        <Button
          flex={1}
          onPress={handleNext}
          disabled={!isDisabled}
          color={!isDisabled ? colors.gray : colors.success}>
          <Text white p opacity={!isDisabled ? 0.5 : 1} bold>
            Confirm Booking
          </Text>
        </Button>
      </Block>
    </Block>
  );
};

const styles = StyleSheet.create({
  calendar: {
    borderRadius: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  timeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  clockIcon: {
    marginRight: 5,
  },
});

export default SelectDateTime;