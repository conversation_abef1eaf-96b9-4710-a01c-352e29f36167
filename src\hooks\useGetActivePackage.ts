export type AppointmentResponse = Record<string, unknown>;

export interface AppointmentState<T> {
  isLoading: boolean;
  error: string | null;
}

import {useState, useEffect, useCallback} from 'react';

import {IAppointment} from '../types';
import useAxiosPrivate from '../lib/use-axios-private';
import {ActivePackageProps} from '../components/ActivePackageCard';

const useGetActivePackage = <T extends AppointmentResponse>() => {
  const axiosPrivate = useAxiosPrivate();

  const [isFetchingActivePackage, setIsFetchingActivePackage] = useState(false);
  const [activePackage, setActivePackage] = useState<ActivePackageProps>();
  const [isFetchingActiveSharedPackage, setIsFetchingActiveSharedPackage] =
    useState(false);
  const [activeSharedPackage, setActiveSharedPackage] =
    useState<ActivePackageProps>();
  const [
    isFetchingActiveUnlimitedPackage,
    setIsFetchingActiveUnlimitedPackage,
  ] = useState(false);
  const [activeUnlimitedPackage, setActiveUnlimitedPackage] =
    useState<ActivePackageProps>();

  const fetchData = useCallback(
    async (endpoint: string): Promise<ActivePackageProps> => {
      const response = await axiosPrivate.get<T>(`appointment/${endpoint}`);
      return response.data as unknown as ActivePackageProps;
    },
    [],
  );

  const fetchActivePackage = async () => {
    setIsFetchingActivePackage(true);
    try {
      const appointments = await fetchData('active-package/');
      setActivePackage(appointments);
      setIsFetchingActivePackage(false);
    } catch (error) {
      setIsFetchingActivePackage(false);
    }
  };

  const fetchActiveSharedPackage = async () => {
    setIsFetchingActiveSharedPackage(true);
    try {
      const appointments = await fetchData('active-shared-package/');
      
      setActiveSharedPackage(appointments);
      setIsFetchingActiveSharedPackage(false);
    } catch (error) {
      setIsFetchingActiveSharedPackage(false);
    }
  };

  const fetchActiveUnlimitedPackage = async () => {
    setIsFetchingActiveUnlimitedPackage(true);
    try {
      const appointments = await fetchData('active-unlimited-package/');
      setActiveUnlimitedPackage(appointments);
      setIsFetchingActiveUnlimitedPackage(false);
    } catch (error) {
      setIsFetchingActiveUnlimitedPackage(false);
    }
  };

  useEffect(() => {
    fetchActivePackage();
  }, []);
  useEffect(() => {
    fetchActiveSharedPackage();
  }, []);
  useEffect(() => {
    fetchActiveUnlimitedPackage();
  }, []);

  return {
    activePackage,
    activeSharedPackage,
    activeUnlimitedPackage,
    isFetchingActivePackage,
    isFetchingActiveSharedPackage,
    isFetchingActiveUnlimitedPackage,
  };
};

export default useGetActivePackage;
