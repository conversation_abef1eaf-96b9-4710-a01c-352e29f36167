import {useEffect, useCallback} from 'react';
import axios from 'axios';
import {useAuth} from '../context/AuthContext'; // Adjust the import path as needed
import {useNavigation} from '@react-navigation/native'; // If you're using React Navigation

export const axiosPrivate = axios.create({
  baseURL: process.env.EXPO_PUBLIC_SERVER_URI,
  headers: {'Content-Type': 'application/json'},
});

const useAxiosPrivate = () => {
  const authValue = useAuth();
  const navigation = useNavigation();

  const logout = useCallback(() => {
    authValue?.logout();
    navigation.navigate('Login' as never);
  }, [authValue, navigation]);

  useEffect(() => {
    const requestIntercept = axiosPrivate.interceptors.request.use(
      (config) => {
        if (!config.headers['Authorization']) {
          config.headers['Authorization'] =
            `Bearer ${authValue?.auth?.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error),
    );

    const responseIntercept = axiosPrivate.interceptors.response.use(
      (response) => response,
      async (error) => {
        const prevRequest = error?.config;
        console.log('error', error);
        // Handle 401 Unauthorized
        if (error?.response?.status === 401) {
          prevRequest.sent = true;
          try {
            const newAccessToken = await authValue?.refreshToken();
            if (!newAccessToken) throw new Error('Failed to refresh token');
            prevRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;
            return axiosPrivate(prevRequest);
          } catch (refreshError) {
            console.error('Token refresh failed', refreshError);
            logout();
          }
        }

        return Promise.reject(error); // Pass the error along
      },
    );

    return () => {
      axiosPrivate.interceptors.request.eject(requestIntercept);
      axiosPrivate.interceptors.response.eject(responseIntercept);
    };
  }, [authValue, logout]);

  return axiosPrivate;
};

export default useAxiosPrivate;
