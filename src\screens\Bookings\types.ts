export interface Service {
  id: number;
  name: string;
  description?: string;
  durations: {time: number; price: number; id?: string | number}[];
}

export interface Package {
  id: number;
  name: string;
  description?: string;
  options: {time: number; price: number; id?: string | number}[];
  servicesIncluded?: string[];
  benefits?: string[];
  totalDuration?: number;
}

export interface Location {
  id: number;
  name: string;
  address: string;
  image: any;
  location: string;
}

export interface Therapist {
  id: string;
  name: string;
  image: any;
  qualifications?: string;
}

export interface ServiceValue {
  id: number;
  name: string;
  time: number;
  price: number;
  type?: string;
  durationId?: string | number;
}

export interface BookingData {
  services: ServiceValue[] | null;
  serviceType: string;
  package: ServiceValue | null;
  location: Location | null;
  therapist: Therapist | null;
  date: string | null;
  time: string | null;
  allocatedMinutes?: string | null;
  user_package_id?: number | null;
  shared_package_id?: number | null;
  unlimited_package_id?: number | null;
}
