
import { useState, useEffect, useCallback } from "react"
import type * as Notifications from "expo-notifications"
import {
  registerForPushNotificationsAsync,
  getNotificationPermissions,
  scheduleLocalNotification,
} from "../lib/notifications"

interface UseNotificationsReturn {
  expoPushToken: string | null
  notification: Notifications.Notification | null
  isLoading: boolean
  error: string | null
  permissions: Notifications.NotificationPermissionsStatus | null
  refreshToken: () => Promise<void>
  scheduleTestNotification: (title: string, body: string) => Promise<void>
}

export function useNotifications(): UseNotificationsReturn {
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null)
  const [notification, setNotification] = useState<Notifications.Notification | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [permissions, setPermissions] = useState<Notifications.NotificationPermissionsStatus | null>(null)

  const refreshToken = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const token = await registerForPushNotificationsAsync()
      setExpoPushToken(token)

      const perms = await getNotificationPermissions()
      setPermissions(perms)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to get push token"
      setError(errorMessage)
      console.error("Error refreshing push token:", err)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const scheduleTestNotification = useCallback(async (title: string, body: string) => {
    try {
      await scheduleLocalNotification(title, body, { test: true })
    } catch (err) {
      console.error("Error scheduling test notification:", err)
    }
  }, [])

  useEffect(() => {
    refreshToken()
  }, [refreshToken])

  return {
    expoPushToken,
    notification,
    isLoading,
    error,
    permissions,
    refreshToken,
    scheduleTestNotification,
  }
}
