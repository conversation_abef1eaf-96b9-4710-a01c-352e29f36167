import {useEffect, useCallback, useState} from 'react';
import axios from 'axios';
import {useAuth} from '../context/AuthContext'; // Adjust the import path as needed
import {useNavigation} from '@react-navigation/native'; // If you're using React Navigation

export const axiosPrivate = axios.create({
  baseURL: process.env.EXPO_PUBLIC_SERVER_URI,
  headers: {'Content-Type': 'application/json'},
});

const useAxiosPrivate = () => {
  const authValue = useAuth();
  const navigation = useNavigation();
  const [isRefreshing, setIsRefreshing] = useState(false);
  let failedQueue: {
    resolve: (token: string | null) => void;
    reject: (error: any) => void;
  }[] = [];

  const processQueue = (error: unknown, token = null) => {
    failedQueue.forEach((prom) => {
      if (error) {
        prom.reject(error);
      } else {
        prom.resolve(token);
      }
    });

    failedQueue = [];
  };

  const logout = useCallback(() => {
    if (authValue?.logout) {
      authValue.logout();
      navigation.navigate('Login' as never);
    }
  }, [authValue, navigation]);

  useEffect(() => {
    const requestIntercept = axiosPrivate.interceptors.request.use(
      (config) => {
        if (!config.headers['Authorization'] && authValue?.auth?.accessToken) {
          config.headers['Authorization'] =
            `Bearer ${authValue.auth.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error),
    );

    const responseIntercept = axiosPrivate.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // If the error is 401 and we haven't already tried to refresh the token
        if (error?.response?.status === 401 && !originalRequest._retry) {
          console.log('error', error);
          if (isRefreshing) {
            // If we're already refreshing, add this request to the queue
            return new Promise((resolve, reject) => {
              failedQueue.push({resolve, reject});
            })
              .then((token) => {
                originalRequest.headers['Authorization'] = `Bearer ${token}`;
                return axiosPrivate(originalRequest);
              })
              .catch((err) => {
                return Promise.reject(err);
              });
          }

          originalRequest._retry = true;
          setIsRefreshing(true);

          try {
            console.log('Attempting to refresh token...');
            const newAccessToken = await authValue?.refreshToken();
            console.log(
              'New access token received:',
              newAccessToken ? 'Yes' : 'No',
            );

            if (!newAccessToken) {
              throw new Error('Failed to refresh token');
            }

            // Update the authorization header
            originalRequest.headers['Authorization'] =
              `Bearer ${newAccessToken}`;

            // Process any requests that were waiting
            processQueue(null, newAccessToken);

            return axiosPrivate(originalRequest);
          } catch (refreshError) {
            console.error('Token refresh failed', refreshError);
            processQueue(refreshError, null);
            logout();
            return Promise.reject(refreshError);
          } finally {
            setIsRefreshing(false);
          }
        }

        return Promise.reject(error);
      },
    );

    return () => {
      axiosPrivate.interceptors.request.eject(requestIntercept);
      axiosPrivate.interceptors.response.eject(responseIntercept);
    };
  }, [authValue, logout, isRefreshing]);

  return axiosPrivate;
};

export default useAxiosPrivate;
