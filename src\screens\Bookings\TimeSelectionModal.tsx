import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  TouchableWithoutFeedback,
} from 'react-native';
import {Block, Text, Button} from '../../components'; /// Adjust import path as needed
import {Feather} from '@expo/vector-icons';
import {useTheme} from '../../hooks';
import {useBooking} from '../../context/BookingContext';
import useGetAllocatedTime from '../../hooks/useGetAllocatedTime';
// import { Block } from '../../components';

const TimeSelectionModal = ({
  handleTimeSelect,
}: {
  handleTimeSelect: (time: string) => void;
}) => {
  const {bookingData, updateBookingData} = useBooking();
  const {sizes, colors} = useTheme();
  const {timeChoices} = useGetAllocatedTime();
  const [modalVisible, setModalVisible] = useState(false);

  const toggleModal = () => {
    setModalVisible(!modalVisible);
  };

  const selectTime = (time: string) => {
    handleTimeSelect(time);
    toggleModal();
  };

  // Get the current selected time's max value
  const selectedMax = bookingData.time
    ? (timeChoices as unknown as Record<string, number>)[bookingData.time]
    : null;

  return (
    <Block card padding={sizes.sm} marginBottom={sizes.m}>
      <Text p semibold marginBottom={sizes.s}>
        Select Time
      </Text>

      <TouchableOpacity
        style={[styles.dropdownButton, {borderColor: colors.gray}]}
        onPress={toggleModal}>
        <Feather
          name="clock"
          size={20}
          color={colors.text}
          style={styles.dropdownIcon}
        />

        {bookingData.time ? (
          <Text p>
            {bookingData.time} - ({selectedMax} mins)
          </Text>
        ) : (
          <Text p color={colors.gray}>
            Select a time
          </Text>
        )}

        <Feather
          name="chevron-down"
          size={20}
          color={colors.text}
          style={styles.dropdownArrow}
        />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={toggleModal}>
        <TouchableWithoutFeedback onPress={toggleModal}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View
                style={[styles.modalContent, {backgroundColor: colors.card}]}>
                <Block row justify="space-between" padding={sizes.sm}>
                  <Text h5>Select Time</Text>
                  <TouchableOpacity onPress={toggleModal}>
                    <Feather name="x" size={24} color={colors.text} />
                  </TouchableOpacity>
                </Block>

                <FlatList
                  data={Object.entries(timeChoices)}
                  keyExtractor={([time]) => time}
                  renderItem={({item: [time, max]}) => (
                    <TouchableOpacity
                      style={[
                        styles.timeItem,
                        bookingData.time === time && {
                          backgroundColor: colors.primary, // 20% opacity
                        },
                      ]}
                      onPress={() => selectTime(time)}>
                      <Block row align="center">
                        <Feather
                          name="clock"
                          size={16}
                          color={
                            bookingData.time === time
                              ? colors.primary
                              : colors.text
                          }
                          style={styles.timeItemIcon}
                        />
                        <Text
                          p
                          color={
                            bookingData.time === time
                              ? colors.primary
                              : colors.text
                          }>
                          {time} - (Max {max} mins)
                        </Text>
                      </Block>

                      {bookingData.time === time && (
                        <Feather
                          name="check"
                          size={20}
                          color={colors.primary}
                        />
                      )}
                    </TouchableOpacity>
                  )}
                  style={styles.timeList}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </Block>
  );
};

const styles = StyleSheet.create({
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    position: 'relative',
  },
  dropdownIcon: {
    marginRight: 10,
  },
  dropdownArrow: {
    position: 'absolute',
    right: 12,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    maxHeight: '70%',
    borderRadius: 10,
    overflow: 'hidden',
  },
  timeList: {
    maxHeight: 300,
  },
  timeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  timeItemIcon: {
    marginRight: 10,
  },
});

export default TimeSelectionModal;
