import React, {createContext, ReactNode, useEffect, useState} from 'react';
import axios from 'axios';
import * as SecureStore from 'expo-secure-store';

const API_URL = process.env.EXPO_PUBLIC_SERVER_URI;
export interface IUserData {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  role: string;
  address: string | null;
  date_of_birth: string | null;
  profile_picture: string | null;
  reward_balance?: {
    total_points: number;
  };
  rewards?: IRewards[];
}

export interface IRewards {
  id: number;
  points: number;
  reward_date: string;
  created_at: string;
}
interface AuthContextType {
  auth: {
    accessToken: string | null;
    refreshToken: string | null;
  };
  login: (email: string, otp: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  setUserData: (data: IUserData) => void;
  userData: IUserData | null;
  isInitialized: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);
interface AuthProviderProps {
  children: ReactNode;
}
export const NewAuthProvider = ({children}: AuthProviderProps) => {
  const [auth, setAuth] = useState({
    accessToken: '',
    refreshToken: '',
  });
  const [userData, setUserData] = useState<IUserData | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const loadStoredData = async () => {
      try {
        const accessToken = await SecureStore.getItemAsync('accessToken');
        const refreshToken = await SecureStore.getItemAsync('refreshToken');
        const userDataString = await SecureStore.getItemAsync('userData');
      
        setAuth({
          accessToken: accessToken || '',
          refreshToken: refreshToken || '',
        });
        if (userDataString) {
          setUserData(JSON.parse(userDataString));
        }
      } catch (error) {
        console.error('Error loading stored data:', error);
      } finally {
        setIsInitialized(true);
      }
    };
    loadStoredData();
  }, []);

  const login = async (email: string, otp: string) => {
    try {
      const response = await axios.post(`${API_URL}/auth/token/`, {email, otp});
      if (!response.data.access || !response.data.refresh) {
        throw new Error('Invalid login response. Tokens missing.');
      }
      setAuth({
        accessToken: response.data.access,
        refreshToken: response.data.refresh,
      });
      await SecureStore.setItemAsync('refreshToken', response.data.refresh);
      await SecureStore.setItemAsync('accessToken', response.data.access);
      return response.data.access; // Return access token for immediate use
    } catch (error) {
      console.error(
        'Login failed:',
        error,
        // error.response?.data?.error || error.message,
      );
      throw error;
    }
  };

  const refreshToken = async () => {
    try {
      const response = await axios.post(`${API_URL}/auth/token/refresh/`, {
        refresh: auth.refreshToken,
      });
      setAuth((prev) => ({...prev, accessToken: response.data.access}));
      await SecureStore.setItemAsync('accessToken', response.data.access);
      return response.data.access; // Return the new access token
    } catch (error) {
      console.error(
        'Token refresh failed:',
        error,
        // error.response?.data?.error || error.message,
      );
      throw error;
    }
  };

  const logout = async () => {
    await SecureStore.deleteItemAsync('accessToken');
    await SecureStore.deleteItemAsync('refreshToken');
    await SecureStore.deleteItemAsync('userData');
    setAuth({accessToken: '', refreshToken: ''});
    setUserData(null);
  };

  return (
    <AuthContext.Provider
      value={{
        auth,
        login,
        refreshToken,
        setUserData,
        userData,
        logout,
        isInitialized,
      }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => React.useContext(AuthContext);
