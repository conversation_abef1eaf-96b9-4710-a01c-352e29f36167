## [1.1.1] 2023-07-19

- Removed unused code
- Add additonal dependencies for react-native-gesture-handler support

## [1.1.0] 2023-05-15

### Updated dependencies

-Expo sdk updated from 45 to 48
-react update from 17 to 18
-react native version updated 0.68 to 0.71.7
-Other packages updated according to expo sdk.
-typescript updated 4.3.5 to 4.5
-expo-app-loading package is deprecated. Added a new package expo-splash-screen. For this,App.js file

## [1.0.2] 2022-07-21

### Updated dependencies

- Updated @react-native-async-storage/async-storage@1.15.0 to @react-native-async-storage/async-storage@1.17.3
- Updated expo@^44.0.0 to expo@45.0.0
- Updated expo-app-loading@~1.3.0 to expo-app-loading@2.0.0
- Updated expo-blur@~11.0.0 to expo-blur@11.1.0
- Updated expo-constants@~13.0.1 to expo-constants@13.1.1
- Updated expo-haptics@~11.1.0 to expo-haptics@11.2.0
- Updated expo-linear-gradient@~11.0.3 to expo-linear-gradient@11.3.0
- Updated expo-localization@~12.0.0 to expo-localization@13.0.0
- Updated expo-module-core@^0.2.0 to expo-module-core@0.9.2
- Updated expo-status-bar@~1.2.0 to expo-status-bar@1.3.0
- Updated react@17.0.1 to react@17.0.2
- Updated react-dom@17.0.1 to react-dom@17.0.2
- Updated react-native@0.64.3 to react@0.68.2
- Updated react-native-gesture-handler@2.1.0 to react-native-gesture-handler@2.2.1
- Updated react-native-pager-view@5.4.9 to react-native-pager-view@5.4.15
- Updated react-native-reanimated@~2.3.1 to react-native-reanimated@2.8.0
- Updated react-native-safe-area-context@3.3.2 to react-native-safe-area-context@4.2.4
- Updated react-native-screens@~3.10.1 to react-native-screens@3.11.1
- Updated react-native-web@~0.17.1 to react-native-web@0.17.1
- Updated dev dependencies
- Updated @types/react-native@0.64.12 to @types/react-native@0.67.6
- Updated jest-expo@44.0.0 to jest-expo@45.0.0
