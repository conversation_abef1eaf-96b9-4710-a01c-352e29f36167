import React, {useCallback, useState} from 'react';
import {Platform, ScrollView, StyleSheet} from 'react-native';
import {Block, Text, Button} from '../../components';
import {useTheme} from '../../hooks';
import {Feather} from '@expo/vector-icons';
import SelectLocation from './Step2SelectLocation';
import SelectTherapist from './Step3SelectTherapist';
import SelectDateTime from './Step4SelectDateTime';
import BookingSummary from './Step5BookingSummary';
import SelectService2 from './Step1SelectService';
import BookingStepIndicator from './BookingStepIndicator';
import BookingConfirmation from './Step6BookingConfirmation';
import {useNavigation} from '@react-navigation/native';
import {BookingData} from './types';
import {useBooking} from '../../context/BookingContext';
import Constants from 'expo-constants';
const BookingFlow = () => {
  const {sizes, colors, gradients} = useTheme();
  const navigation = useNavigation();
  const [step, setStep] = useState(1);

  const {bookingData, setBookingData} = useBooking();
  const isStepComplete = (currentStep: number): boolean => {
    // Only validate steps 1-4
    if (currentStep < 5) {
      switch (currentStep) {
        case 1:
          return (
            (bookingData?.services && bookingData?.services.length > 0) ||
            bookingData.package !== null
          );
        case 2:
          return bookingData.location !== null;
        case 3:
          return bookingData.therapist !== null;
        case 4:
          return bookingData.date !== null && bookingData.time !== null;
        default:
          return false;
      }
    }

    // Always return true for steps 5 and above
    return true;
  };

  const handleNext = useCallback(() => {
    if (isStepComplete(step)) {
      setStep((prevStep) => prevStep + 1);
    }
  }, [step, isStepComplete]);

  const handlePrevious = () => {
    setStep((prevStep) => prevStep - 1);
  };

  const handleNavigateHome = () => {
    setBookingData({
      services: [],
      package: null,
      location: null,
      therapist: null,
      allocatedMinutes: null,
      date: null,
      time: null,
    });
    navigation.navigate('Home' as never);
    setStep(1);
  };

  const handleBookAnother = () => {
    setBookingData({
      services: [],
      package: null,
      location: null,
      therapist: null,
      allocatedMinutes: null,
      date: null,
      time: null,
    });
    navigation.navigate('Bookings' as never);
    setStep(1);
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return <SelectService2 />;
      case 2:
        return <SelectLocation />;
      case 3:
        return <SelectTherapist />;
      case 4:
        return <SelectDateTime handleNext={handleNext} />;
      case 5:
        return <BookingSummary handleNext={handleNext} />;
      case 6:
        return (
          <BookingConfirmation
            onBookAnother={handleBookAnother}
            onHome={handleNavigateHome}
          />
        );
      default:
        return null;
    }
  };

  const renderBookingSummary = () => {
    if (step === 1) return null;

    // Check if selected package is an unlimited package
    const isUnlimitedPackage = 
      bookingData.package?.type === 'unlimited_package' || 
      bookingData.package?.type === 'my_unlimited_package';

    return (
      <Block
        card
        padding={sizes.sm}
        marginBottom={sizes.m}
        style={{
          paddingTop:
            Platform.OS === 'android' ? Constants.statusBarHeight + 30 : 30,
        }}>
        <Text h5 semibold marginBottom={sizes.sm}>
          Your Booking Overview:
        </Text>

        <Block>
          <Text p semibold marginBottom={sizes.s}>
            Your Services so far:
          </Text>
          {bookingData.package ? (
            <Block>
              <Block
                align="center"
                justify="space-between"
                row
                marginBottom={sizes.s}>
                <Block row>
                  <Text p semibold>
                    {bookingData.package.name}
                  </Text>
                  {/* Only show minutes for non-unlimited packages */}
                  {!isUnlimitedPackage && (
                    <Text p size={12} marginLeft={sizes.xs}>
                      ({bookingData.package.time} mins)
                    </Text>
                  )}
                </Block>

                <Text p bold>
                  AED {bookingData.package.price}
                </Text>
              </Block>
            </Block>
          ) : (
            bookingData.services?.map((service) => {
              return (
                <Block
                  align="center"
                  justify="space-between"
                  row
                  key={service.id}
                  marginBottom={sizes.s}>
                  <Block row>
                    <Text p semibold>
                      {service.name}
                    </Text>
                    <Text p size={12} marginLeft={sizes.s}>
                      ({service.time} mins)
                    </Text>
                  </Block>

                  <Text p bold>
                    AED {service.price}
                  </Text>
                </Block>
              );
            })
          )}
        </Block>

        {bookingData.allocatedMinutes && (
          <Block row align="center" marginTop={-2}>
            <Feather name="clock" size={15} color={colors.primary} />
            <Text p marginLeft={6}>
              Allocated Minutes -
            </Text>
            <Text p marginLeft={6} gray>
              {bookingData.allocatedMinutes}
            </Text>
          </Block>
        )}

        {bookingData.location && (
          <Block row align="center" marginTop={sizes.s}>
            <Feather name="map-pin" size={15} color={colors.primary} />
            <Text p marginLeft={6}>
              {bookingData.location.name}
            </Text>
          </Block>
        )}
        {bookingData.therapist && (
          <Block row align="center" marginTop={sizes.s}>
            <Feather name="user" size={15} color={colors.primary} />
            <Text p marginLeft={6}>
              {bookingData.therapist.name}
            </Text>
          </Block>
        )}
        {/* calendar */}
        {bookingData.date && bookingData.time && (
          <Block row align="center" marginTop={sizes.s}>
            <Feather name="calendar" size={15} color={colors.primary} />
            <Block row align="center" marginLeft={6}>
              <Text p semibold>
                {bookingData.date}
              </Text>
              <Text p size={14} marginLeft={6}>
                at
              </Text>

              <Text p semibold marginLeft={6}>
                {bookingData.time}
              </Text>
            </Block>
          </Block>
        )}
      </Block>
    );
  };

  return (
    <Block safe style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
      <Block padding={sizes.text} paddingBottom={140}>
  <Button
    style={styles.backButton}
    onPress={() => navigation.goBack()}>
    <Feather name="arrow-left" size={24} color={colors.primary} />
  </Button>
  
  <Text h4 center marginBottom={sizes.m}>
    Book Your Appointment
  </Text>


          <BookingStepIndicator step={step} />

          {renderStep()}
          <Block row marginVertical={sizes.padding}>
            {step > 1 && step < 5 && (
              <Button
                black
                flex={1}
                marginRight={sizes.s}
                onPress={handlePrevious}>
                <Text white bold transform="uppercase">
                  Previous
                </Text>
              </Button>
            )}
            {step < 4 && (
              <Button
                flex={1}
                marginLeft={step > 1 ? sizes.s : 0}
                onPress={
                  step < 5
                    ? handleNext
                    : () => console.log('Booking confirmed:', bookingData)
                }
                disabled={step < 5 && !isStepComplete(step)}
                color={
                  step < 5 && !isStepComplete(step)
                    ? colors.gray
                    : colors.primary
                }>
                <Text
                  white
                  opacity={step < 5 && !isStepComplete(step) ? 0.5 : 1}
                  bold>
                  {step < 5
                    ? isStepComplete(step)
                      ? 'Next'
                      : 'Complete This Step'
                    : 'Confirm Booking'}
                </Text>
              </Button>
            )}
          </Block>
          {step < 5 && renderBookingSummary()}
        </Block>
      </ScrollView>
    </Block>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    paddingTop: Platform.OS === 'android' ? Constants.statusBarHeight : 0,
  },
  backButton: {
    position: 'absolute',
    left: 16,
    top: 8,
    zIndex: 10,
    backgroundColor: 'transparent',
  },
});


export default BookingFlow;

