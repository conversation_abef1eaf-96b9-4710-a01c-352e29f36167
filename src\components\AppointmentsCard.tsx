import React from 'react';
import {TouchableOpacity, StyleSheet} from 'react-native';
import {
  Feather,
  AntDesign,
  FontAwesome,
  MaterialIcons,
  Ionicons,
} from '@expo/vector-icons';

import Block from './Block';
import Text from './Text';
import {useTheme, useTranslation} from '../hooks/';
import {AppointmentResponse} from '../hooks/useAppointments';
import {Package_Obj} from '../types/package_obj';

interface IAppointmentCard extends AppointmentResponse {
  user?: 'user' | 'therapist';
  type?: 'home';
  client_name: string;
  therapist_name: string;
  appointment_date: string;
  time?: string; // Added time property
  service_name: string;
  service_duration: number;
  reward_points?: number;
  location_name: string;
  remainingTime?: number | null;
  status?: string;
  total_price?: number;
  package_balance_obj?: Package_Obj | null;

  unlimitedPackage?: string | null;
  userPackage?: string | null;
  sharedPackage?: string | null;
}

const AppointmentCard = ({
  client_name,
  therapist_name,
  appointment_date,
  time, // Added time parameter
  service_name,
  service_duration,
  location_name,
  notes,
  user = 'user',
  type,
  remainingTime,
  reward_points = 0,
  status = 'booked',
  total_price = 0,
  package_balance_obj = null,
  unlimitedPackage,
  userPackage,
  sharedPackage,
}: IAppointmentCard) => {
  const {t} = useTranslation();
  const {colors, sizes} = useTheme();

  const formatDate = (dateString: string, timeString?: string) => {
    // Create date from dateString
    const date = new Date(dateString);
    
    // If time is provided separately, set the time
    if (timeString) {
      const [hours, minutes, seconds] = timeString.split(':').map(Number);
      date.setHours(hours, minutes, seconds);
    }
    
    return {
      day: date.toLocaleString('en-US', {weekday: 'short'}),
      date: date.toLocaleString('en-US', {day: 'numeric', month: 'short'}),
      time: date.toLocaleString('en-US', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: true,
      }),
    };
  };

  const isUnlimitedPackage = unlimitedPackage !== null

  const {day, date, time: formattedTime} = formatDate(appointment_date as string, time);

  // Status badge color mapping
  const getStatusColor = () => {
    switch (status) {
      case 'check_in':
        return colors.success;
      case 'completed':
        return colors.primary;
      case 'cancelled':
        return colors.danger;
      case 'booked':
      default:
        return colors.secondary;
    }
  };

  // Calculate package usage percentage if package data exists
  const getPackageRemainingUsage = () => {
    if (package_balance_obj) {
      const {remaining_time, total_time} = package_balance_obj;
      return Math.round((remaining_time / total_time) * 100);
    }
    return null;
  };

  const getPackagePercentageUsage = () => {
    if (package_balance_obj) {
      const {remaining_time, total_time} = package_balance_obj;
      return Math.round(((total_time - remaining_time) / total_time) * 100);
    }
    return null;
  };

  const packageRemainingUsage = getPackagePercentageUsage();
  const packageUsage = getPackageRemainingUsage();
  const formatExpiryDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const activeServiceName =
    service_name ||
    userPackage ||
    sharedPackage ||
    unlimitedPackage ||
    'Service';

  // const formattedExpiryDate = formatExpiryDate(expiry_date);
  return (
    <Block
      card
      flex={0}
      paddingHorizontal={sizes.m}
      paddingVertical={type == 'home' ? 0 : sizes.sm} /* Reduced from sizes.m to sizes.sm */
      marginBottom={sizes.sm} /* Reduced from sizes.sm to sizes.xs */
      width={'100%'}
      style={{
        shadowColor: colors.black,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
      }}>
      {type == 'home' ? (
        <Block>
          <Block row justify="space-between" align="center">
            <Block row align="center">
              <Text h5 bold color={colors.primary}>
                {day}
              </Text>
              <Text h5 semibold marginLeft={10}>
                {date}
              </Text>
            </Block>
            <Block align="flex-end">
              <Text size={sizes.sm} semibold color={colors.secondary}>
                {formattedTime}
              </Text>
            </Block>
          </Block>
        </Block>
      ) : (
        <Block>
          {/* Package Remaining Time Banner - More compact */}
          {remainingTime && user !== 'therapist' && (
            <Block
              flex={0}
              style={{
                backgroundColor: colors?.success,
                paddingHorizontal: sizes.xs,
                paddingVertical: 4,
                borderRadius: 12,
                justifyContent: 'center',
                alignItems: 'center',
                width: 160,
                marginBottom: 6,
              }}>
              <Text size={12} semibold color={colors.white}>
                {remainingTime} min(s) remaining
              </Text>
            </Block>
          )}

          {/* Date and Time Section - More compact */}
          <Block row justify="space-between" marginBottom={sizes.xs}>
            <Block>
              <Text h4 bold color={colors.primary}>
                {day}
              </Text>
              <Text p semibold>
                {date}
              </Text>
            </Block>
            <Block align="flex-end">
              <Text p semibold color={colors.secondary}>
                {formattedTime}
              </Text>
              <Block align="center" row>
                <Feather
                  name="clock"
                  size={14}
                  color={colors.gray}
                  style={{marginRight: 3}}
                />
                <Text size={12} color={colors.gray}>
                  {service_duration} min
                </Text>
              </Block>
            </Block>
          </Block>

          {/* Therapist/Client Section - More compact */}
          <Block row marginBottom={sizes.xs} align="center">
            <Block flex={0} marginRight={sizes.xs}>
              {user == 'therapist' ? (
                <AntDesign name="user" size={16} color={colors.primary} />
              ) : (
                <FontAwesome name="user-md" size={16} color={colors.primary} />
              )}
            </Block>
            <Text p semibold>
              {user == 'therapist' ? client_name : therapist_name}
            </Text>
          </Block>

          {/* Service Section - Hide for therapists if it's a package type, more compact */}
          {activeServiceName && (user !== 'therapist' || (user === 'therapist' && service_name && !userPackage && !sharedPackage && !unlimitedPackage)) && (
            <Block row marginBottom={sizes.xs} align="center">
              <Block flex={0} marginRight={sizes.xs}>
                <Feather name="activity" size={16} color={colors.primary} />
              </Block>
              <Block row justify="space-between" width="90%">
                <Text size={14} semibold>
                  {activeServiceName}
                </Text>
              </Block>
            </Block>
          )}
          {total_price > 0 && (
            <Block row marginBottom={sizes.xs} align="center">
              <Block flex={0} marginRight={sizes.xs}>
                <Ionicons
                  name="pricetag-outline"
                  size={16}
                  color={colors.primary}
                />
              </Block>
              <Block row justify="space-between" width="90%">
                <Text size={14} semibold>
                  AED {total_price}
                </Text>
              </Block>
            </Block>
          )}

          {/* Location Section - More compact */}
          <Block row marginBottom={sizes.xs} align="center">
            <Block flex={0} marginRight={sizes.xs}>
              <Feather name="map-pin" size={16} color={colors.primary} />
            </Block>
            <Text size={14} semibold>
              {location_name}
            </Text>
          </Block>

          {/* Reward Points Section - More compact */}
          {user !== 'therapist' && reward_points > 0 && (
            <Block row marginBottom={sizes.xs} align="center">
              <Block flex={0} marginRight={sizes.xs}>
                <MaterialIcons name="redeem" size={16} color={colors.primary} />
              </Block>
              <Text size={14} semibold>
                {`${reward_points ?? 0} - Reward pts.`}
              </Text>
            </Block>
          )}

          {/* Package Usage Section - Only visible to users, not therapists - More compact */}
          {packageUsage !== null && user !== 'therapist' && !unlimitedPackage  && (
            <Block marginVertical={sizes.xs}>
              <Block row justify="space-between" marginBottom={sizes.xs}>
                <Text size={14} semibold>
                  Package Usage 
                </Text>
                <Text size={14} semibold color={colors.primary}>
                  {package_balance_obj?.remaining_time} /{' '}
                  {package_balance_obj?.total_time} min
                </Text>
              </Block>
              <Block
                row
                flex={0}
                height={4}
                radius={2}
                color={colors.gray}
                marginBottom={sizes.xs}>
                {packageRemainingUsage !== null && (
                  <Block
                    flex={0}
                    radius={2}
                    width={`${packageRemainingUsage}%`}
                    color={colors.primary}
                  />
                )}
              </Block>
              <Text size={12} color={colors.gray} align="right">
                {packageUsage}% remaining
              </Text>

              <Block row marginTop={sizes.xs} align="center">
                <Block flex={0} marginRight={sizes.xs}>
                  <Feather name="calendar" size={16} color={colors.primary} />
                </Block>
                <Text size={12}>
                  <Text size={12}>Expires on</Text>{' '}
                  <Text size={14} semibold>
                    {formatExpiryDate(
                      package_balance_obj?.expiry_date as string,
                    )}
                  </Text>
                </Text>
              </Block>
            </Block>
          )}

          {/* Notes Section - More compact */}
          {notes ? (
            <Block
              padding={sizes.xs}
              color={colors.card}
              radius={sizes.cardRadius}
              marginVertical={sizes.xs}>
              <Text size={14} semibold color={colors.text} marginBottom={2}>
                {t('common.extraNotes')}:
              </Text>
              <Text size={13} color={colors.text}>
                {notes as string}
              </Text>
            </Block>
          ) : null}

          {/* Status Badge - More compact */}
          {status && (
            <Block row justify="flex-end">
              <Block
                flex={0}
                style={{
                  backgroundColor: getStatusColor(),
                  paddingHorizontal: 12,
                  paddingVertical: 4,
                  borderRadius: 12,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Block row align="center">
                  <Ionicons
                    name="checkmark-circle"
                    size={14}
                    color={colors.white}
                    style={{marginRight: 3}}
                  />
                  <Text size={11} semibold color={colors.white}>
                    {status == 'check_in' ? 'Checked In' : status}
                  </Text>
                </Block>
              </Block>
            </Block>
          )}
          {/* Status Badge - Top Right */}
          {/* <Block
            flex={0}
            style={{
              backgroundColor: getStatusColor(),
              paddingHorizontal: sizes.xs,
              paddingVertical: sizes.xs / 2,
              borderRadius: 12,
              zIndex: 1,
            }}>
            <Text size={12} semibold color={colors.white}>
              {formatStatus(status)}
            </Text>
          </Block> */}
        </Block>
      )}
    </Block>
  );
};

export default AppointmentCard;