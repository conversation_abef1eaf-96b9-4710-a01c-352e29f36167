import React, {useEffect, useState} from 'react';
import {Block, Text, Button} from '../../components/';
import {useTheme} from '../../hooks/';
import {BookingData} from './types';

import {
  StyleSheet,
  TouchableOpacity,
  View,
  Text as NativeText,
  ActivityIndicator,
} from 'react-native';
import Entypo from '@expo/vector-icons/Entypo';
import useServicesAndPackages from '../../hooks/useServicesAndPackages';
import {IDuration, IPackagesPayload, IServicesPayload} from '../../types';
import {useBooking} from '../../context/BookingContext';
import useGetActivePackage from '../../hooks/useGetActivePackage';

interface SelectedPackage {
  id: number;
  time: number;
}

// Props for the component
interface PackageListProps {
  type: string;
  packages: IPackagesPayload[];
  isFetchingPackages: boolean;
  selectedPackage: SelectedPackage | null;
  handlePackageSelect: (
    type: string,
    pkg: IPackagesPayload,
    option: IDuration,
  ) => void;
}
const RenderPackageOptions: React.FC<PackageListProps> = ({
  type,
  packages,
  isFetchingPackages,
  selectedPackage,
  handlePackageSelect,
}) => {
  const [visibleBenefits, setVisibleBenefits] = useState<
    Record<number, boolean>
  >({});

  // Toggle benefits visibility for a specific package
  const toggleBenefits = (packageId: number) => {
    setVisibleBenefits((prev) => ({
      ...prev,
      [packageId]: !prev[packageId],
    }));
  };
  const {sizes, colors} = useTheme();
  
  // Check if this is an unlimited package type
  const isUnlimitedPackage = type === 'unlimited_package' || type === 'my_unlimited_package';
  
  const renderPackageOptions = () => (
    <>
      {isFetchingPackages ? (
        <Block card padding={sizes.sm} center flex={1}>
          <ActivityIndicator size="large" color={colors.primary} />
        </Block>
      ) : (
        packages.map((pkg) => {
          const showBenefits = visibleBenefits[pkg?.id as number] || false;

          return (
            <Block
              key={pkg.name + pkg.id}
              card
              paddingVertical={25}
              style={{borderColor: colors.gray}}
              marginBottom={sizes.md}
              padding={sizes.text}>
              <Text h4 semibold marginBottom={sizes.xs} center>
                {pkg.name}
              </Text>

              {/* Toggle Benefits Button */}
              <TouchableOpacity
                onPress={() => toggleBenefits(pkg?.id as number)}>
                <Text
                  color={colors.gray}
                  size={sizes.text}
                  center
                  marginBottom={sizes.s}>
                  {showBenefits ? 'Hide Benefits' : 'Show Benefits'}
                </Text>
              </TouchableOpacity>

              {/* Benefits and Description Section */}
              {showBenefits ? (
                <>
                  <Text
                    color={colors.black}
                    size={sizes.text}
                    p
                    center
                    marginBottom={sizes.s}>
                    {pkg.description}
                  </Text>

                  {/* Benefits List */}
                  {pkg.benefits && pkg.benefits.length > 0 && (
                    <View style={styles.benefitsContainer}>
                      {pkg.benefits.map((benefit, index) => (
                        <Text
                          key={index}
                          color={colors.black}
                          size={sizes.text}
                          p
                          center>
                          {benefit}
                        </Text>
                      ))}
                    </View>
                  )}

                  {/* Included Services Section */}
                  {pkg.services_included &&
                    pkg.services_included.length > 0 && (
                      <>
                        <Text
                          color={colors.black}
                          size={sizes.text}
                          semibold
                          center
                          marginTop={sizes.sm}
                          marginBottom={sizes.xs}>
                          Included Services:
                        </Text>
                        <View style={styles.servicesContainer}>
                          {pkg.services_included.map((service) => (
                            <View key={service.id} style={styles.serviceItem}>
                              <Text
                                color={colors.black}
                                size={sizes.text}
                                center>
                                {service.name}
                              </Text>
                            </View>
                          ))}
                        </View>
                      </>
                    )}
                </>
              ) : null}

              {/* Duration Options */}
              <View style={styles.durationOptions}>
                {pkg.options.map((option) => {
                  const isSelected =
                    selectedPackage?.id === pkg.id &&
                    selectedPackage?.time === Number(option.time);

                  return (
                    <TouchableOpacity
                      style={[
                        styles.durationButton,
                        isSelected
                          ? styles.selectedDurationButton
                          : styles.unselectedDurationButton,
                      ]}
                      onPress={() => handlePackageSelect(type, pkg, option)}
                      key={option.time}>
                      <NativeText
                        style={[
                          styles.durationText,
                          isSelected
                            ? styles.selectedDurationText
                            : styles.unselectedDurationText,
                        ]}>
                        {/* Display for unlimited packages vs regular packages */}
                        {isUnlimitedPackage 
                          ? `Unlimited Access - ${option.price} AED`
                          : `${option.time} min - ${option.price} AED`}
                      </NativeText>

                      {isSelected && (
                        <Entypo name="check" size={24} color="#32CD32" />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </Block>
          );
        })
      )}
    </>
  );

  return renderPackageOptions();
};

export default RenderPackageOptions;

const styles = StyleSheet.create({
  durationOptions: {
    marginTop: 15,
  },
  durationButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 8, // Matches `sizes.radius`
    padding: 12, // Matches `sizes.m`
    marginBottom: 12, // Matches `sizes.s`
  },
  selectedDurationButton: {
    backgroundColor: '#DFFFD6', // Light green background
    borderColor: '#32CD32', // Green border
    borderWidth: 1,
  },
  unselectedDurationButton: {
    backgroundColor: '#F5F5F5', // Light gray background
    borderColor: '#CCCCCC', // Light gray border
    borderWidth: 1,
  },
  durationText: {
    fontSize: 16, // Matches `sizes.medium`
    fontWeight: '500',
  },
  selectedDurationText: {
    color: '#32CD32', // Green text for selected
  },
  unselectedDurationText: {
    color: '#333333', // Dark gray text for unselected
  },
  benefitsContainer: {
    marginTop: 10,
    marginBottom: 10,
  },
  servicesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: 5,
    marginBottom: 15,
  },
  serviceItem: {
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 10,
    margin: 5,
    minWidth: 120,
    alignItems: 'center',
  },
});