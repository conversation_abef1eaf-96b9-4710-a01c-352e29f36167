import React from 'react';
import {StyleSheet} from 'react-native';
import {Block, Text, Button} from '../../components';
import {useTheme} from '../../hooks';
import {Feather} from '@expo/vector-icons';
import {ServiceValue} from './types';
import {useBooking} from '../../context/BookingContext';

interface BookingConfirmationProps {
  onHome?: () => void;
  onBookAnother?: () => void;
}

const BookingConfirmation: React.FC<BookingConfirmationProps> = ({
  onHome,
  onBookAnother,
}) => {
  const {bookingData} = useBooking();
  const {sizes, colors} = useTheme();
  
  const calculateTotal = () => {
    if (bookingData.package) {
      return bookingData.package.price;
    }
    return bookingData.services
      ? bookingData.services.reduce(
          (total, service) => total + service.price,
          0,
        )
      : 0;
  };

  const isUnlimitedPackage = bookingData.package?.type === 'my_unlimited_package' || 
                             bookingData.package?.type === 'unlimited_package';

  const renderServiceItem = (service: ServiceValue) => {
    // Hide the minutes display for unlimited packages
    const showMinutes = !(service.type === 'unlimited_package' || service.type === 'my_unlimited_package');
    
    return (
      <Block key={service.id} row justify="space-between" marginBottom={sizes.xs}>
        <Block row align="center">
          <Block
            style={styles.serviceDot}
            color={colors.primary}
            marginRight={sizes.xs}
            flex={0}
          />
          <Text p>
            {service.name} 
            {showMinutes && <Text size={12}> ({service.time} mins)</Text>}
          </Text>
        </Block>
        <Text p semibold>
          {service.price} AED
        </Text>
      </Block>
    );
  };

  return (
    <Block safe padding={sizes.padding}>
      <Block card padding={sizes.m} style={styles.successCard}>
        <Block align="center" marginBottom={sizes.l}>
          <Block
            style={styles.checkCircle}
            color={colors.primary}
            marginBottom={sizes.m}>
            <Feather name="check" size={32} color={colors.white} />
          </Block>
          <Text h4 center marginBottom={sizes.s}>
            Thank You for Booking!
          </Text>
          <Text p center color={colors.text}>
            Your booking has been confirmed
          </Text>
        </Block>

        <Block style={styles.section}>
          <Text h5 marginBottom={sizes.s}>
            Services Booked
          </Text>
          {bookingData.package ? (
            <Block>
              {renderServiceItem(bookingData.package)}
              <Text p color={colors.text} marginTop={sizes.xs}>
                Booked time: {bookingData.allocatedMinutes} minutes
              </Text>
            </Block>
          ) : bookingData.services ? (
            bookingData.services.map(renderServiceItem)
          ) : null}
        </Block>

        <Block style={styles.section}>
          <Text h5 marginBottom={sizes.s}>
            Appointment Details
          </Text>

          <Block row align="center" marginBottom={sizes.s}>
            <Feather
              name="map-pin"
              size={20}
              color={colors.primary}
              style={styles.icon}
            />
            <Text p>{bookingData.location?.name}</Text>
          </Block>

          <Block row align="center" marginBottom={sizes.s}>
            <Feather
              name="user"
              size={20}
              color={colors.primary}
              style={styles.icon}
            />
            <Text p>{bookingData.therapist?.name}</Text>
          </Block>

          <Block row align="center">
            <Feather
              name="calendar"
              size={20}
              color={colors.primary}
              style={styles.icon}
            />
            <Block>
              <Text p>{bookingData.date}</Text>
              <Text p color={colors.text}>
                {bookingData.time}
              </Text>
            </Block>
          </Block>
        </Block>

        <Block style={styles.section}>
          <Block row justify="space-between" marginBottom={sizes.s}>
            <Text h5>Total Amount</Text>
            <Text h5>{calculateTotal()} AED</Text>
          </Block>
        </Block>

        <Block row marginTop={sizes.l}>
          <Button
            flex={1}
            onPress={onHome}
            color={colors.primary}
            marginRight={sizes.s}>
            <Text white bold transform="uppercase">
              Go to Home
            </Text>
          </Button>
          <Button
            flex={1}
            onPress={onBookAnother}
            color={colors.white}
            // borderWidth={1}
            // borderColor={colors.primary}
          >
            <Text primary bold transform="uppercase">
              Book Another
            </Text>
          </Button>
        </Block>
      </Block>
    </Block>
  );
};

const styles = StyleSheet.create({
  successCard: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  checkCircle: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  section: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  icon: {
    marginRight: 12,
  },
  serviceDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});

export default BookingConfirmation;