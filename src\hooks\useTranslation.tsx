import { I18n } from "i18n-js";
import * as Localization from 'expo-localization';
import Storage from '@react-native-async-storage/async-storage';
import React, { useCallback, useContext, useEffect, useState } from 'react';

import translations from '../constants/translations/';  // Make sure translations is imported correctly
import { ITranslate } from '../constants/types';

export const TranslationContext = React.createContext({});

export const TranslationProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [locale, setLocale] = useState('en');
  const i18n = new I18n(translations);
  // Set the locale once at the beginning of your app.
  i18n.locale = locale;

  // When a value is missing from a language it'll fallback to another language with the key present.
  i18n.enableFallback  = true;

  const t = useCallback(
    (scope: I18n.Scope, options?: I18n.TranslateOptions) => {
      return i18n.t(scope, { ...options, locale });
    },
    [locale],
  );

  // Get locale from storage
  const getLocale = useCallback(async () => {
    // Get preference from storage
    const localeJSON = await Storage.getItem('locale');

    // Set Locale / compare if has updated
    setLocale(localeJSON !== null ? localeJSON : Localization.locale);
  }, [setLocale]);

  useEffect(() => {
    getLocale();
  }, [getLocale]);

  useEffect(() => {
    // Save preference to storage
    Storage.setItem('locale', locale);
  }, [locale]);

  const contextValue = {
    t,
    locale,
    setLocale,
    translate: t,
  };

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  );
};

/*
 * useTranslation hook based on i18n-js
 * Source: https://github.com/fnando/i18n-js
 */
export const useTranslation = () =>
  useContext(TranslationContext) as ITranslate;
