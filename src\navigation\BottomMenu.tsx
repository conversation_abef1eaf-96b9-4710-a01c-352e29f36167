import React from 'react';
import {TouchableOpacity, StyleSheet, ViewStyle} from 'react-native';
import {
  createBottomTabNavigator,
  BottomTabBarProps,
} from '@react-navigation/bottom-tabs';
import {Feather} from '@expo/vector-icons';

import {Block, Text, Image} from '../components';
import {useScreenOptions, useTheme} from '../hooks';
import {Profile, PointsAndRank, UserHome, Home} from '../screens';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import NewProfilePage from '../screens/NewProfile';
import ImprovedProfilePage from '../screens/NewProfile2';
import BookingFlow from '../screens/Bookings/MainBookingFlow';
import Appointments from '../screens/Appointments';
import Commission from '../screens/Commission';
import {useAuth} from '../context/AuthContext';

type RootTabParamList = {
  Rewards: undefined;
  Home: undefined;
  Profile: undefined;
  Appointments: undefined;
};

const Tab = createBottomTabNavigator<RootTabParamList>();

const CustomTabBar: React.FC<BottomTabBarProps> = ({
  state,
  descriptors,
  navigation,
}) => {
  const {colors, assets} = useTheme();
  const insets = useSafeAreaInsets();

  return (
    <Block
      row
      color={colors.white}
      style={[
        styles.container,
        {paddingBottom: insets.bottom, height: 60 + insets.bottom},
      ]}>
      {state.routes.map((route, index) => {
        const {options} = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
              ? options.title
              : route.name;

        const isFocused = state.index === index;
        const isHome = route.name === 'Homex';

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        return (
          <TouchableOpacity
            key={route.key}
            onPress={onPress}
            style={[
              styles.tab,
              isHome && styles.homeTab,
              //   isFocused && !isHome && styles.focusedTab,
            ]}>
            {isHome ? (
              <Block style={styles.homeButton} align="center" justify="center">
                <Block
                  radius={33}
                  width={66}
                  height={66}
                  marginTop={-20}
                  flex={0}
                  white={!isFocused}
                  primary={isFocused}
                  //   gradient={!isFocused ? [''] : gradients.primary}
                  align="center"
                  justify="center">
                  <Image
                    source={assets.home}
                    style={{width: 25, height: 25}}
                    tintColor={isFocused ? colors.white : colors.gray}
                  />
                </Block>
                <Text
                  p
                  marginBottom={19}
                  semibold={isFocused}
                  color={isFocused ? colors.primary : colors.gray}
                  size={14}>
                  {label.toString()}
                </Text>
              </Block>
            ) : (
              <>
                <Feather
                  name={
                    route.name === 'Rewards'
                      ? 'gift'
                      : route.name === 'Profile'
                        ? 'user'
                        : 'home'
                  }
                  size={24}
                  color={isFocused ? colors.primary : colors.gray}
                />
                <Text
                  p
                  bold={isFocused}
                  color={isFocused ? colors.primary : colors.gray}
                  size={14}>
                  {label.toString()}
                </Text>
              </>
            )}
          </TouchableOpacity>
        );
      })}
    </Block>
  );
};

const Menu2: React.FC = () => {
  const {colors} = useTheme();
  const screenOptions = useScreenOptions();
  const authData = useAuth();
  const userData = authData?.userData;
  const isTherapist = userData?.role === 'therapist';

  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
      initialRouteName="Home">
      {isTherapist ? (
        <>
          <Tab.Screen
            name="Appointments"
            component={isTherapist ? Home : UserHome}
          />
          {/* <Tab.Screen name="Home" component={Home} /> */}
          <Tab.Screen name="Profile" component={NewProfilePage} />
        </>
      ) : (
        <>
          <Tab.Screen name="Home" component={isTherapist ? Home : UserHome} />
          {/* <Tab.Screen name="Home" component={Home} /> */}
          <Tab.Screen name="Profile" component={NewProfilePage} />
        </>
      )}
    </Tab.Navigator>
  );
};

export default Menu2;

interface Styles {
  container: ViewStyle;
  tab: ViewStyle;
  homeTab: ViewStyle;
  homeButton: ViewStyle;
  //   focusedTab: ViewStyle;
}

const styles = StyleSheet.create<Styles>({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  homeTab: {
    // paddingBottom: 25,
  },
  homeButton: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  //   focusedTab: {
  //     transform: [{scale: 1.1}],
  //   },
});
