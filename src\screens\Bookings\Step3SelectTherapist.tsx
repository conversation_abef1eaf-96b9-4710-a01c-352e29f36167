import React, {useCallback, useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Image,
  ImageSourcePropType,
  StyleSheet,
} from 'react-native';
import {Block, Text, Button} from '../../components/';
import {useTheme} from '../../hooks/';
import {Therapist} from './types';
import {Feather} from '@expo/vector-icons';
import {useAuth} from '../../context/AuthContext';
import {axiosPrivate} from '../../lib/use-axios-private';
import {ITherapistData, ITherapistResponseData} from '../../types';
import {useBooking} from '../../context/BookingContext';

const SelectTherapist = () => {
  const {bookingData, updateBookingData} = useBooking();
  const {sizes, colors, assets} = useTheme();
  const [isLoading, setIsLoading] = useState(true);

  const therapists: Therapist[] = [
    {
      id: '1',
      name: '<PERSON><PERSON>',
      image: assets.male_therapist,
    },
    {
      id: '2',
      name: '<PERSON>',
      image: assets.therapist2,
    },
    {
      id: '3',
      name: '<PERSON> <PERSON>najjar',
      image: assets.male_therapist,
    },
    {
      id: '4',
      name: '<PERSON> Dell',
      image: assets.male_therapist,
    },
    {
      id: '5',
      name: 'Farnaz Alidadi',
      image: assets.female_therapist,
    },
    {
      id: '6',
      name: 'Usman Zaman',
      image: assets.male_therapist,
    },
    {
      id: '7',
      name: 'Fatimah Abdullah',
      image: assets.female_therapist,
    },
    {
      id: '8',
      name: 'Khatera Fatihi',
      image: assets.female_therapist,
    },
    {
      id: '9',
      name: 'Troy Reonel Salinas',
      image: assets.male_therapist,
    },
    {
      id: '10',
      name: 'Katherine Joy Salgarino',
      image: assets.female_therapist,
    },
    {
      id: '11',
      name: 'Maryam Bautista',
      image: assets.female_therapist,
    },
  ];

  const handleSelectTherapist = (therapist: Therapist) => {
    updateBookingData('therapist', therapist);
  };

  const [therapistsData, setTherapistsData] = useState<ITherapistData[]>([]);
  const authData = useAuth();
  const fetchData = useCallback(
    async (endpoint: string): Promise<ITherapistResponseData[]> => {
      try {
        const response = await axiosPrivate.get<ITherapistResponseData[]>(
          `${endpoint}`,
          {
            headers: {
              Authorization: `Bearer ${authData?.auth?.accessToken}`,
            },
          },
        );
        return response.data;
      } catch (error) {
        console.error(`Error fetching data from ${endpoint}:`, error);
        throw new Error(
          error instanceof Error ? error.message : 'Unknown error occurred',
        );
      }
    },
    [authData?.auth?.accessToken],
  );

  useEffect(() => {
    const fetchTherapistsData = async () => {
      try {
        const response = await fetchData(`/staff/therapists-p/`);
        const mappedData = response
          .filter((thera) => thera?.location == bookingData?.location?.location)
          .map((therapist) => {
            const match = therapists.find(
              (h) => h.name === therapist.full_name,
            );

            return {
              id: therapist.id,
              name: therapist.full_name,
              image:
              therapist?.gender == 'male'
                ? assets?.male_therapist
                : assets?.female_therapist,
              qualifications: therapist.qualifications,
            };
          });

        setTherapistsData(mappedData);
      } catch (error) {
        console.error('Error fetching therapists data new ome:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTherapistsData();
  }, []);
  return (
    <Block>
      <Text h5 semibold marginBottom={sizes.m}>
        Choose a Therapist
      </Text>
      <Block row wrap="wrap" justify="space-between">
        {isLoading ? (
          <Block card padding={sizes.sm} center flex={1}>
            <ActivityIndicator size="large" color={colors.primary} />
          </Block>
        ) : (
          therapistsData.map((therapist) => {
            const isSelected = bookingData.therapist?.id === therapist.id;
            return (
              <Button
                key={therapist.id}
                onPress={() => handleSelectTherapist(therapist)}
                color={isSelected ? colors.primary : colors.card}
                marginBottom={sizes.m}
                padding={sizes.sm}
                width={(sizes.width - sizes.padding * 3) / 1.9}
                style={styles.therapistButton}>
                <Block align="center">
                  <Image
                    source={therapist.image as ImageSourcePropType}
                    style={[
                      styles.therapistImage,
                      {borderColor: isSelected ? colors.white : colors.primary},
                    ]}
                  />
                  <Text
                    p
                    semibold
                    center
                    marginTop={sizes.s}
                    marginBottom={sizes.xs}
                    color={isSelected ? colors.white : colors.black}>
                    {therapist.name}
                  </Text>
                  <Text
                    size={sizes.text}
                    center
                    color={isSelected ? colors.white : colors.gray}>
                    {therapist.qualifications}
                  </Text>
                </Block>
                {isSelected && (
                  <Block style={styles.checkmarkContainer}>
                    <Feather
                      name="check-circle"
                      size={24}
                      color={colors.white}
                    />
                  </Block>
                )}
              </Button>
            );
          })
        )}
      </Block>
    </Block>
  );
};

const styles = StyleSheet.create({
  therapistButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  therapistImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
  },
  checkmarkContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 12,
    padding: 4,
  },
});

export default SelectTherapist;
