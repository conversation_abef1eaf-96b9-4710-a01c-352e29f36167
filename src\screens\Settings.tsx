import {useNavigation} from '@react-navigation/core';
import React from 'react';

import {useData, useTheme, useTranslation} from '../hooks/';
import {Block, Button, Image, Switch, Text} from '../components/';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
const Settings = () => {
  const navigation = useNavigation();
  const {isDark, handleIsDark} = useData();
  const {t, locale, setLocale} = useTranslation();
  const {assets, colors, gradients, sizes} = useTheme();

  const isEN = locale.includes('en');

  return (
    <Block
      safe
      scroll
      padding={sizes.padding}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{paddingBottom: sizes.xxl}}
      style={{
        paddingTop:
          Platform.OS === 'android' ? Constants.statusBarHeight + 30 : 30,
      }}>
      <Button
        row
        flex={0}
        justify="flex-start"
        marginBottom={10}
        onPress={() => navigation.goBack()}>
        <Image
          radius={0}
          width={10}
          height={18}
          color={colors.black}
          source={assets.arrow}
          transform={[{rotate: '180deg'}]}
        />
        <Text p black marginLeft={sizes.s}>
          {/* {t('profile.title')} */}
          Back
        </Text>
      </Button>

      {/* settings */}
      <Block card padding={sizes.sm} marginBottom={sizes.sm}>
        <Block row align="center" marginBottom={sizes.m}>
          <Block
            flex={0}
            align="center"
            justify="center"
            radius={sizes.s}
            width={sizes.md}
            height={sizes.md}
            marginRight={sizes.s}
            gradient={gradients.primary}>
            <Image source={assets?.settings} color={colors.white} radius={0} />
          </Block>
          <Block>
            {/* <Text semibold>{t('settings.recommended.title')}</Text> */}
            <Text semibold>Settings</Text>
            <Text size={12}>{t('settings.recommended.subtitle')}</Text>
          </Block>
        </Block>

        {/* dark mode */}
        <Block
          row
          align="center"
          justify="space-between"
          marginBottom={sizes.sm}>
          <Text>{t('settings.recommended.darkmode')}</Text>
          <Switch
            checked={isDark}
            onPress={(checked) => handleIsDark(checked)}
          />
        </Block>

        {/* language */}
        {/* <Block
          row
          align="center"
          justify="space-between"
          marginBottom={sizes.m}>
          <Text>{t('settings.recommended.language')} EN/FR</Text>
          <Switch
            checked={!isEN}
            onPress={(checked) => setLocale(checked ? 'fr' : 'en')}
          />
        </Block> */}

        {/* face id */}
        <Block
          row
          align="center"
          justify="space-between"
          marginBottom={sizes.sm}>
          <Text>{t('settings.recommended.faceid')}</Text>
          <Switch checked />
        </Block>

        {/* <Block row align="center" justify="space-between">
          <Text>{t('settings.recommended.autolock')}</Text>
          <Switch />
        </Block>
        <Button
          row
          align="center"
          justify="space-between"
          onPress={() => navigation.navigate('NotificationsSettings')}>
          <Text>{t('settings.recommended.notifications')}</Text>
          <Image
            source={assets.arrow}
            color={colors.icon}
            radius={0}
            height={18}
            width={10}
          />
        </Button> */}
      </Block>

      {/* payment */}
      {/* <Block card padding={sizes.sm} marginBottom={sizes.sm}>
        <Block row align="center" marginBottom={sizes.s}>
          <Block
            flex={0}
            align="center"
            justify="center"
            radius={sizes.s}
            width={sizes.md}
            height={sizes.md}
            marginRight={sizes.s}
            gradient={gradients.primary}>
            <Image source={assets?.payment} color={colors.white} radius={0} />
          </Block>
          <Block>
            <Text semibold>{t('settings.payment.title')}</Text>
            <Text size={12}>{t('settings.payment.subtitle')}</Text>
          </Block>
        </Block>
        <Button row align="center" justify="space-between">
          <Text>{t('settings.payment.options')}</Text>
          <Image
            source={assets.arrow}
            color={colors.icon}
            radius={0}
            height={18}
            width={10}
          />
        </Button>
        <Button row align="center" justify="space-between">
          <Text>{t('settings.payment.giftcards')}</Text>
          <Image
            source={assets.arrow}
            color={colors.icon}
            radius={0}
            height={18}
            width={10}
          />
        </Button>
      </Block> */}

      {/* privacy */}
      <Block card padding={sizes.sm} marginBottom={sizes.sm}>
        <Block row align="center" marginBottom={sizes.s}>
          <Block
            flex={0}
            align="center"
            justify="center"
            radius={sizes.s}
            width={sizes.md}
            height={sizes.md}
            marginRight={sizes.s}
            gradient={gradients.primary}>
            <Image source={assets?.document} color={colors.white} radius={0} />
          </Block>
          <Block>
            <Text semibold>{t('settings.privacy.title')}</Text>
            <Text size={12}>{t('settings.privacy.subtitle')}</Text>
          </Block>
        </Block>
        <Button
          row
          align="center"
          justify="space-between"
          onPress={() => navigation.navigate('Agreement')}>
          <Text>{t('settings.privacy.agreement')}</Text>
          <Image
            source={assets.arrow}
            color={colors.icon}
            radius={0}
            height={18}
            width={10}
          />
        </Button>
        <Button
          row
          align="center"
          justify="space-between"
          onPress={() => navigation.navigate('Privacy')}>
          <Text>{t('settings.privacy.privacy')}</Text>
          <Image
            source={assets.arrow}
            color={colors.icon}
            radius={0}
            height={18}
            width={10}
          />
        </Button>
        {/* <Button
          row
          align="center"
          justify="space-between"
          onPress={() => navigation.navigate('About')}>
          <Text>FAQ</Text>
          <Image
            source={assets.arrow}
            color={colors.icon}
            radius={0}
            height={18}
            width={10}
          />
        </Button> */}
      </Block>
    </Block>
  );
};

export default Settings;
