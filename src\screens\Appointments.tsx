import React, {useCallback, useState} from 'react';
import {Platform, StyleSheet, ActivityIndicator} from 'react-native';
import Constants from 'expo-constants';
import {useData, useTheme, useTranslation} from '../hooks/';
import {Block, Button, Image, Text} from '../components/';
import AppointmentCard from '../components/AppointmentsCard';

const Appointments = () => {
  const {t} = useTranslation();
  const [tab, setTab] = useState<number>(0);
  const {allAppointments, todaysAppointments} = useData();
  const [appointments, setAppointments] = useState(allAppointments);
  const {assets, colors, fonts, gradients, sizes} = useTheme();

  const handleTabChange = useCallback(
    (tabIndex: number) => {
      setTab(tabIndex);
      setAppointments(tabIndex === 0 ? allAppointments : todaysAppointments);
    },
    [todaysAppointments, allAppointments],
  );

  return (
    <Block safe style={styles.container}>
      {/* Header */}
      <Block flex={0} style={styles.header} gradient={gradients?.primary}>
        <Text h4 white font="bold" style={styles.headerTitle}>
          {t('home.appointments')}
        </Text>
      </Block>

      {/* Toggle tabs */}
      <Block
        row
        flex={0}
        align="center"
        justify="center"
        paddingBottom={sizes.sm}>
        <Button onPress={() => handleTabChange(0)}>
          <Block row align="center">
            <Block
              flex={0}
              radius={6}
              align="center"
              justify="center"
              marginRight={sizes.s}
              width={sizes.socialIconSize}
              height={sizes.socialIconSize}
              gradient={gradients?.[tab === 0 ? 'primary' : 'secondary']}>
              <Image source={assets.components} color={colors.white} radius={0} />
            </Block>
            <Text p font={fonts?.[tab === 0 ? 'medium' : 'normal']}>
              {t('home.all')}
            </Text>
          </Block>
        </Button>
        <Block
          gray
          flex={0}
          width={1}
          marginHorizontal={sizes.sm}
          height={sizes.socialIconSize}
        />
        <Button onPress={() => handleTabChange(1)}>
          <Block row align="center">
            <Block
              flex={0}
              radius={6}
              align="center"
              justify="center"
              marginRight={sizes.s}
              width={sizes.socialIconSize}
              height={sizes.socialIconSize}
              gradient={gradients?.[tab === 1 ? 'primary' : 'secondary']}>
              <Image
                radius={0}
                color={colors.white}
                source={assets.calendar}
              />
            </Block>
            <Text p font={fonts?.[tab === 1 ? 'medium' : 'normal']}>
              {t('home.today')}
            </Text>
          </Block>
        </Button>
      </Block>

      {/* Appointments list */}
      <Block
        scroll
        paddingHorizontal={sizes.padding}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}>
        <Block style={styles.appointmentsContainer}>
          {appointments && appointments.length > 0 ? (
            <Block>
              {appointments.map((appointment) => (
                <AppointmentCard {...appointment} key={`card-${appointment?.id}`} />
              ))}
            </Block>
          ) : (
            <Block padding={sizes.md} style={styles.emptyStateContainer}>
              <Text p color={colors.gray} center>
                There are no bookings to display now...
              </Text>
            </Block>
          )}
        </Block>
      </Block>
    </Block>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    paddingTop: Platform.OS === 'android' ? Constants.statusBarHeight : 0,
  },
  header: {
    paddingVertical: 3,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    marginBottom: 6,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTitle: {
    textAlign: 'center',
    marginVertical: 8,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  appointmentsContainer: {
    marginTop: 8,
  },
  loadingContainer: {
    minHeight: 200,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  emptyStateContainer: {
    minHeight: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.01)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    marginTop: 12,
  }
});

export default Appointments;