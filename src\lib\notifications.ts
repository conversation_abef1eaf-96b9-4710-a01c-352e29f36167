import { Platform } from "react-native"
import * as Device from "expo-device"
import * as Notifications from "expo-notifications"
import Constants from "expo-constants"

class NotificationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = "NotificationError"
  }
}

function handleRegistrationError(errorMessage: string): never {
  console.error("Push notification registration error:", errorMessage)
  throw new NotificationError(errorMessage)
}

export async function registerForPushNotificationsAsync(): Promise<string> {
  // Check if running on a physical device
  if (!Device.isDevice) {
    handleRegistrationError("Push notifications require a physical device")
  }

  // Configure Android notification channel
  if (Platform.OS === "android") {
    await Notifications.setNotificationChannelAsync("default", {
      name: "Default",
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: "#FF231F7C",
      sound: "default",
    })
  }

  try {
    // Check existing permissions
    const { status: existingStatus } = await Notifications.getPermissionsAsync()
    let finalStatus = existingStatus

    // Request permissions if not already granted
    if (existingStatus !== "granted") {
      const { status } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
        },
      })
      finalStatus = status
    }

    // Handle permission denial
    if (finalStatus !== "granted") {
      handleRegistrationError(
        "Push notification permissions not granted. Please enable notifications in your device settings.",
      )
    }

    // Get project ID from Expo config
    const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId

    if (!projectId) {
      handleRegistrationError("Project ID not found. Make sure your app is properly configured with EAS.")
    }

    // Get the push token
    const pushTokenData = await Notifications.getExpoPushTokenAsync({
      projectId,
    })

    if (!pushTokenData?.data) {
      handleRegistrationError("Failed to get push token from Expo")
    }

    // console.log("Successfully registered for push notifications:", pushTokenData.data)
    return pushTokenData.data
  } catch (error) {
    if (error instanceof NotificationError) {
      throw error
    }

    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred"
    handleRegistrationError(`Failed to register for push notifications: ${errorMessage}`)
  }
}

// Helper function to schedule a local notification (useful for testing)
export async function scheduleLocalNotification(
  title: string,
  body: string,
  data?: Record<string, any>,
  seconds = 1,
): Promise<string> {
  return await Notifications.scheduleNotificationAsync({
    content: {
      title,
      body,
      data,
      sound: "default",
    },
    trigger: {
      type: "timeInterval",
      seconds,
      repeats: false,
    },
  })
}

// Helper function to cancel all scheduled notifications
export async function cancelAllScheduledNotifications(): Promise<void> {
  await Notifications.cancelAllScheduledNotificationsAsync()
}

// Helper function to get notification permissions status
export async function getNotificationPermissions(): Promise<Notifications.NotificationPermissionsStatus> {
  return await Notifications.getPermissionsAsync()
}
