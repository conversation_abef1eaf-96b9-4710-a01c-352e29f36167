import React, {useEffect, useRef} from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import {
  About,
  Agreement,
  Components,
  Extras,
  Home,
  Notifications,
  Privacy,
  Profile,
  Settings,
  Shopping,
  NotificationsSettings,
} from '../screens';

import {useScreenOptions, useTranslation} from '../hooks';
import Appointments from '../screens/Appointments';
import PointsAndRank from '../screens/PointsAndRank';
import UserHome from '../screens/UserHome';
import Menu2 from './BottomMenu';
import NewLogin from '../screens/auth/NewLogin';
import NewRegister from '../screens/auth/NewRegister';
import NewVerifyToken from '../screens/auth/NewVerifyToken';
import ProtectedRoute from '../screens/auth/ProtectedRoute';
import BookingFlow from '../screens/Bookings/MainBookingFlow';
import BookingProvider from '../context/BookingContext';
import {useAuth} from '../context/AuthContext';
import {useNavigation} from '@react-navigation/native';
import UserBookingHistory from '../screens/UserBookingHistory';
import UserActivePackage from '../screens/UserActivePackage';

const Stack = createStackNavigator();

export default () => {
  const navigationIsReady = useRef(false);
  const {t} = useTranslation();
  const screenOptions = useScreenOptions();

  const authData = useAuth();
  const navigation = useNavigation();

  useEffect(() => {
    // Only attempt navigation if auth is initialized and navigation is ready
    if (authData?.isInitialized) {
      // Use a small timeout to ensure navigation is ready
      const timer = setTimeout(() => {
        navigationIsReady.current = true;
        try {
          if (!authData?.auth?.accessToken || !authData?.userData) {
            navigation.reset({
              index: 0,
              routes: [{name: 'Login'}],
            });
          } else {
            navigation.reset({
              index: 0,
              routes: [{name: 'MainTabs'}],
            });
          }
        } catch (error) {
          console.error('Navigation error:', error);
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [
    authData?.isInitialized,
    authData?.auth?.accessToken,
    authData?.userData,
  ]);

  return (
    <Stack.Navigator screenOptions={{...screenOptions.stack}}>
      <>
        {authData?.userData && (
          <>
            <Stack.Screen
              name="MainTabs"
              component={ProtectedRoute(Menu2)}
              // component={Menu2}
            />
            <Stack.Screen
              name="Bookings"
              component={ProtectedRoute(BookingFlow)}
              options={{title: 'Book Now'}}
            />

            <Stack.Screen
              name="Appointments"
              component={Appointments}
              options={{title: t('navigation.appointments')}}
            />
            <Stack.Screen
              name="PointsAndRank"
              component={PointsAndRank}
              options={{title: t('navigation.pointsAndRank')}}
            />
            <Stack.Screen
              name="Components"
              component={Components}
              options={screenOptions.components}
            />

            <Stack.Screen
              name="BookingHistory"
              component={UserBookingHistory}
              options={{title: 'Booking History', ...screenOptions.rental}}
              // options={{headerShown: false}}
            />
            <Stack.Screen
              name="MyActivePackage"
              component={UserActivePackage}
              options={{title: 'Active Package', ...screenOptions.rental}}
              // options={{headerShown: false}}
            />

            <Stack.Screen
              name="Profile"
              component={Profile}
              options={{headerShown: false}}
            />

            <Stack.Screen
              name="Settings"
              component={Settings}
              // options={{title: t('navigation.settings'), ...screenOptions.profile}}
            />
            <Stack.Screen
              name="NotificationsSettings"
              component={NotificationsSettings}
              options={{
                title: t('navigation.notifications'),
                ...screenOptions.back,
              }}
            />
            <Stack.Screen
              name="Notifications"
              component={Notifications}
              options={{
                title: t('navigation.notifications'),
                ...screenOptions.back,
              }}
            />
            <Stack.Screen
              name="Agreement"
              component={Agreement}
              options={{
                title: t('navigation.agreement'),
                ...screenOptions.back,
              }}
            />
            <Stack.Screen
              name="About"
              component={About}
              options={{title: t('navigation.about'), ...screenOptions.back}}
            />
            <Stack.Screen
              name="Privacy"
              component={Privacy}
              options={{title: t('navigation.privacy'), ...screenOptions.back}}
            />
          </>
        )}
        <>
          <Stack.Screen
            name="Login"
            component={NewLogin}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="Register"
            component={NewRegister}
            options={{headerShown: false}}
          />

          <Stack.Screen
            name="VerifyToken"
            component={NewVerifyToken}
            options={{headerShown: false}}
          />
        </>
      </>
    </Stack.Navigator>
  );
};
