import React, {useState} from 'react';
import {
  StyleSheet,
  Platform,
  Text as NativeText,
  View,
  ActivityIndicator,
} from 'react-native';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/core';
import axios from 'axios';
import {
  <PERSON>Field,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import * as SecureStore from 'expo-secure-store';
import {useData, useTheme, useTranslation} from '../../hooks/';
import * as regex from '../../constants/regex';
import {Block, Button, Text} from '../../components/';
import {IUserData, useAuth} from '../../context/AuthContext';
import useAxiosPrivate from '../../lib/use-axios-private';
// import {useAuth, UserData} from '../../hooks/useAuth';

const isAndroid = Platform.OS === 'android';
const CELL_COUNT = 6;

// Define the type for the route params
type VerifyTokenRouteProp = RouteProp<
  {
    VerifyToken: {
      email: string;
      isSignUp: boolean;
    };
  },
  'VerifyToken'
>;
const VERIFY_OTP = `/auth/token/verify/`;
const NewVerifyToken = () => {
  const {isDark} = useData();
  // const {setUserData, setIsAuthenticated} = useAuth();
  const {t} = useTranslation();
  const navigation = useNavigation();
  const route = useRoute<VerifyTokenRouteProp>();
  const {email, isSignUp} = route.params;
  const [isLoading, setIsLoading] = useState(false);
  const [value, setValue] = useState('');
  const ref = useBlurOnFulfill({value, cellCount: CELL_COUNT});
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });
  const [error, setError] = useState<string | null>(null);
  const {colors, gradients, sizes} = useTheme();

  const styles = StyleSheet.create({
    codeFieldRoot: {
      marginTop: sizes.m,
      marginBottom: sizes.m,
      width: 280,
      marginLeft: 'auto',
      marginRight: 'auto',
    },
    cell: {
      width: 40,
      height: 40,
      lineHeight: 38,
      fontSize: 20,
      borderWidth: 2,
      borderColor: colors.gray,
      textAlign: 'center',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: sizes.s,
    },
    focusCell: {
      borderColor: colors.primary,
    },
    loadingContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      width: '100%',
      backgroundColor: 'transparent',
    },
  });

  const authData = useAuth();

  const axiosPrivate = useAxiosPrivate();

  const handleVerify = async () => {
    if (!regex.otp.test(value)) {
      setError(t('errors.invalidOTP'));
      return;
    }
    try {
      setIsLoading(true);

      const accessToken = await authData?.login(email, value);
      if (!accessToken) {
        throw new Error('No token received');
      }

      try {
        const userResponse = await axiosPrivate.get('/auth/users/me/', {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });
        if (userResponse.data) {
          const userData = userResponse.data;
          await SecureStore.setItemAsync('userData', JSON.stringify(userData));
          await SecureStore.setItemAsync('accessToken', accessToken);

          authData?.setUserData(userData as IUserData);
          // userData
          navigation.navigate('MainTabs', {screen: 'Home'});
        }
      } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
          setError(error.response.data.error || t('errors.invalidOTP'));
          console.error(
            'Error verifying otp response:',
            error.request,
            error.response.data,
          );
        } else {
          setError(t('errors.invalidOTP'));
        }
      }
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        setError(error.response.data.error);
        console.error(
          'Error verifying otp response:',
          error.request,
          error.response.data,
        );
      } else {
        setError(t('errors.invalidOTP'));
      }
      // setError(t('errors.invalidOTP'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Block safe marginTop={sizes.md}>
      <Block paddingHorizontal={sizes.s}>
        <Block
          keyboard
          behavior={!isAndroid ? 'padding' : 'height'}
          marginTop={sizes.xl}
          marginBottom={sizes.xxl}>
          <Block
            flex={0}
            radius={sizes.sm}
            marginHorizontal="3%"
            marginTop="50%"
            shadow={!isAndroid}>
            <Block
              blur
              flex={0}
              intensity={90}
              radius={sizes.sm}
              overflow="hidden"
              justify="space-evenly"
              tint={colors.blurTint}
              paddingVertical={sizes.sm}>
              <Text h4 center bold>
                {isSignUp ? t('common.verifySignUp') : t('common.verifyLogin')}
              </Text>
              <Text p center>
                {t('common.enterOTP')}
              </Text>
              <CodeField
                ref={ref}
                {...props}
                value={value}
                onChangeText={setValue}
                cellCount={CELL_COUNT}
                rootStyle={styles.codeFieldRoot}
                keyboardType="number-pad"
                textContentType="oneTimeCode"
                renderCell={({index, symbol, isFocused}) => (
                  <NativeText
                    key={index}
                    style={[styles.cell, isFocused && styles.focusCell]}
                    onLayout={getCellOnLayoutHandler(index)}>
                    {symbol || (isFocused ? <Cursor /> : null)}
                  </NativeText>
                )}
              />
              {error && (
                <Text color={colors.danger} center marginBottom={sizes.s}>
                  {error}
                </Text>
              )}
              <Button
                onPress={handleVerify}
                marginVertical={sizes.s}
                marginHorizontal={sizes.sm}
                gradient={gradients.primary}
                disabled={value.length !== CELL_COUNT}>
                {isLoading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#fff" />
                  </View>
                ) : (
                  <Text bold white transform="uppercase">
                    {t('common.verify')}
                  </Text>
                )}
              </Button>
            </Block>
          </Block>
        </Block>
      </Block>
    </Block>
  );
};

export default NewVerifyToken;
