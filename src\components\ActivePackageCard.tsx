import React from 'react';
import {
  Feather,
  MaterialIcons,
  FontAwesome5,
  Ionicons,
} from '@expo/vector-icons';

import Block from './Block';
import Text from './Text';
import {useTheme, useTranslation} from '../hooks/';

interface Service {
  id: number;
  name: string;
  description: string;
  durations: {
    id: number;
    time: number;
    price: string;
  }[];
}

interface Package {
  id: number;
  name: string;
  description: string;
  services_included: Service[];
  benefits: string[];
}

interface PackageOption {
  id: number;
  time: number;
  price: string;
  package: Package;
}

export interface ActivePackageProps {
  id: number;
  total_time: number;
  remaining_time: number;
  time_deducted: number;
  active?: boolean;
  package_option: PackageOption;
  expiry_date: string;
}

const ActivePackageCard = ({
  total_time,
  remaining_time,
  time_deducted,
  package_option,
  expiry_date,
  active = false,
}: ActivePackageProps) => {
  const {t} = useTranslation();
  const {colors, sizes} = useTheme();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const formattedExpiryDate = formatDate(expiry_date);
  const packageName = package_option.package.name;
  const usagePercentage = Math.round((remaining_time / total_time) * 100);

  return (
    <Block
      card
      flex={0}
      paddingHorizontal={sizes.m}
      paddingVertical={sizes.m}
      marginBottom={sizes.sm}
      width={'100%'}
      style={{
        shadowColor: colors.black,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
      }}>
      <Block>
        {/* Package Name */}
        <Block row marginBottom={sizes.sm}>
          <Block>
            <Text h5 bold color={colors.primary}>
              {packageName}
            </Text>
          </Block>
        </Block>

        {/* Time Usage Progress */}
        <Block marginBottom={sizes.sm}>
          <Block row justify="space-between" marginBottom={sizes.xs}>
            <Text p semibold>
              Time Remaining
            </Text>
            <Text p semibold color={colors.primary}>
              {remaining_time} / {total_time} min
            </Text>
          </Block>
          <Block
            row
            flex={0}
            height={8}
            radius={4}
            color={colors.background}
            marginBottom={sizes.xs}>
            <Block
              flex={0}
              radius={4}
              width={`${usagePercentage}%`}
              color={colors.primary}
            />
          </Block>
          <Block row justify="space-between">
            <Text p color={colors.gray}>
              {time_deducted} min used
            </Text>
            <Text p color={colors.gray}>
              {usagePercentage}% remaining
            </Text>
          </Block>
        </Block>

        {/* Expiry Date */}
        <Block row marginBottom={sizes.sm} align="center">
          <Block flex={0} marginRight={sizes.s}>
            <Feather name="calendar" size={20} color={colors.primary} />
          </Block>
          <Text p semibold>
            Expires on {formattedExpiryDate}
          </Text>
        </Block>

        {/* Services Included */}
        <Block row marginBottom={sizes.sm} align="center">
          <Block flex={0} marginRight={sizes.s}>
            <MaterialIcons name="spa" size={20} color={colors.primary} />
          </Block>
          <Text p semibold>
            {package_option.package.services_included.length} Services Included
          </Text>
        </Block>

        {/* Services List - First 3 only */}
        <Block marginLeft={sizes.xl} marginBottom={sizes.sm}>
          {package_option.package.services_included
            .slice(0, 3)
            .map((service, index) => (
              <Block row key={index} marginBottom={sizes.xs} align="center">
                <FontAwesome5
                  name="check-circle"
                  size={14}
                  color={colors.success}
                  style={{marginRight: 8}}
                />
                <Text p>{service.name}</Text>
              </Block>
            ))}
          {package_option.package.services_included.length > 3 && (
            <Text
              p
              color={colors.secondary}
              marginLeft={sizes.s}
              marginTop={sizes.xs}>
              +{package_option.package.services_included.length - 3} more
              services
            </Text>
          )}
        </Block>

        {/* Status Badge */}
        {active && (
          <Block row justify="flex-end">
            <Block
              flex={0}
              style={{
                backgroundColor: colors.success,
                paddingHorizontal: sizes.sm,
                paddingVertical: sizes.xs,
                borderRadius: 15,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Block row align="center">
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={colors.white}
                  style={{marginRight: 5}}
                />
                <Text size={14} semibold color={colors.white}>
                  Active
                </Text>
              </Block>
            </Block>
          </Block>
        )}
      </Block>
    </Block>
  );
};

export default ActivePackageCard;
