import React, {useState, useEffect} from 'react';
import {StyleSheet, View} from 'react-native';
import {Block, Text} from '../components';
import {useTheme} from '../hooks';
import Slider from '@react-native-community/slider';

interface CustomSliderProps {
  minValue?: number;
  maxValue: number;
  onValueChange: (value: number) => void;
}

const CustomSlider: React.FC<CustomSliderProps> = ({
  minValue = 5,
  maxValue,
  onValueChange,
}) => {
  const {colors, sizes} = useTheme();
  const [sliderValue, setSliderValue] = useState(minValue);

  useEffect(() => {
    // Ensure the initial value is a multiple of 5
    setSliderValue(Math.ceil(minValue / 5) * 5);
  }, [minValue]);

  const handleValueChange = (value: number) => {
    // Round to nearest multiple of 5
    const roundedValue = Math.round(value / 5) * 5;
    setSliderValue(roundedValue);
    // onValueChange(roundedValue);
  };

  const handleSlidingComplete = (value: number) => {
    const roundedValue = Math.round(value / 5) * 5;
    setSliderValue(roundedValue);
    onValueChange(roundedValue);
  };

  return (
    <Block card padding={sizes.sm} marginVertical={sizes.sm}>
      <Text p semibold marginBottom={sizes.sm}>
        Set Allocated Minutes: {sliderValue} mins
      </Text>
      <Slider
        style={styles.slider}
        minimumValue={minValue}
        maximumValue={maxValue}
        step={5}
        value={sliderValue}
        onValueChange={handleValueChange}
        onSlidingComplete={handleSlidingComplete}
        minimumTrackTintColor={colors.primary as string}
        maximumTrackTintColor={colors.gray as string}
        thumbTintColor={colors.primary as string}
      />
      <Block row justify="space-between" marginTop={sizes.sm}>
        <Text p color={colors.gray}>
          Min: {minValue} mins
        </Text>
        <Text p color={colors.gray}>
          Max: {maxValue} mins
        </Text>
      </Block>
    </Block>
  );
};

const styles = StyleSheet.create({
  slider: {
    width: '100%',
    height: 40,
  },
});

export default CustomSlider;
