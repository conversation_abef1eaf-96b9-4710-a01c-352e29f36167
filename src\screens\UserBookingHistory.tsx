import {useState, useCallback, useEffect} from 'react';
import {
  ActivityIndicator,
  Platform,
  RefreshControl,
  StyleSheet,
} from 'react-native';
import {useTheme, useTranslation} from '../hooks/';
import {Block, Button, Image, Text} from '../components/';
import AppointmentCard from '../components/AppointmentsCard';
import useAppointments from '../hooks/useAppointments';
import Pagination from '../components/Pagination';
import {useNavigation} from '@react-navigation/native';
import {Feather} from '@expo/vector-icons';
import Constants from 'expo-constants';

interface Location {
  id: number;
  name: string;
  address: string;
  image: any;
  location: string;
}

const UserBookingHistory = () => {
  const {t} = useTranslation();
  const [tab, setTab] = useState<number>(1);
  const {
    isLoading,
    pastAppointments,
    todayAppointments,
    nextAppointments,
    pastLoading,
    currentPage,
    totalPages,
    itemsPerPage,
    goToPage,
    refetch,
  } = useAppointments();
  const [displayAppointments, setDisplayAppointments] = useState<any[]>([]);
  const {assets, colors, fonts, gradients, sizes} = useTheme();

  const [refreshing, setRefreshing] = useState(false);

  const navigation = useNavigation();
  useEffect(() => {
    // Set initial display to today's appointments when data loads
    if (todayAppointments) {
      setDisplayAppointments(todayAppointments);
    }
  }, [todayAppointments, refetch]);

  useEffect(() => {
    // Set initial display to all appointments when data loads
    if (tab === 0) {
      setDisplayAppointments(pastAppointments?.results || []);
    }
  }, [pastAppointments, tab, refetch]);

  useEffect(() => {
    // Update displayed appointments when past appointments change
    if (tab === 2) {
      setDisplayAppointments(nextAppointments || []);
    }
  }, [nextAppointments, tab, refetch]);

  const handleTabChange = useCallback(
    (tabIndex: number) => {
      setTab(tabIndex);
      // Update displayed appointments based on selected tab
      if (tabIndex === 0) {
        // For past appointments, we need to use the results array
        setDisplayAppointments(pastAppointments?.results || []);
      } else if (tabIndex === 1) {
        setDisplayAppointments(todayAppointments || []);
      } else if (tabIndex === 2) {
        setDisplayAppointments(nextAppointments || []);
      }
    },
    [pastAppointments, todayAppointments, nextAppointments],
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const locations: Location[] = [
    {
      id: 1,
      name: 'Studio Al Warqa Mall',
      address: 'Al Warqa Mall, Tripoli st, Dubai',
      location: 'A',
      image: assets.gym1,
    },
    {
      id: 2,
      name: 'Studio Al mizhar branch',
      address: 'Al mizhar branch (inside fithub), Dubai',
      location: 'B',
      image: assets.gym2,
    },
  ];
  const TabButton = ({
    index,
    icon,
    label,
  }: {
    index: number;
    icon: any;
    label: string;
  }) => (
    <Button onPress={() => handleTabChange(index)}>
      <Block row align="center">
        <Block
          flex={0}
          radius={6}
          align="center"
          justify="center"
          marginRight={sizes.s}
          width={sizes.socialIconSize}
          height={sizes.socialIconSize}
          gradient={gradients?.[tab === index ? 'primary' : 'secondary']}>
          <Image radius={0} color={colors.white} source={icon} />
        </Block>
        <Text p font={fonts?.[tab === index ? 'medium' : 'normal']}>
          {label}
        </Text>
      </Block>
    </Button>
  );
  // console.log("pastAppointments", pastAppointments)
  // Render pagination info for past appointments
  const renderPaginationInfo = () => {
    if (tab === 0 && pastAppointments?.count > 0 && itemsPerPage > 0) {
      const totalCount = pastAppointments.count;
      const startItem = (currentPage - 1) * itemsPerPage + 1;
      const endItem = Math.min(
        startItem + pastAppointments.results.length - 1,
        totalCount,
      );
      return (
        <Text center marginBottom={sizes.sm} color={colors.gray}>
          Showing {startItem}-{endItem} of {totalCount} appointments
        </Text>
      );
    }
    return null;
  };
  // Render pagination component for past appointments
  const renderPagination = () => {
    if (tab === 0 && totalPages > 1) {
      return (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={goToPage}
          loading={pastLoading}
        />
      );
    }
    return null;
  };
  // Render loading indicator for past appointments pagination
  const renderPastLoadingIndicator = () => {
    if (tab === 0 && pastLoading) {
      return (
        <Block center marginVertical={sizes.sm}>
          <ActivityIndicator size="small" color={colors.primary} />
        </Block>
      );
    }
    return null;
  };

  return (
    <Block safe style={styles.container}>
      {/* Header */}
      <Block flex={0} style={styles.header} gradient={gradients?.primary}>
        <Button style={styles.backButton} onPress={() => navigation.goBack()}>
          <Feather name="arrow-left" size={24} color={colors.white} />
        </Button>

        <Text h4 white font="bold" style={styles.headerTitle}>
          Appointments
        </Text>
      </Block>

      {/* Toggle tabs */}
      <Block
        row
        flex={0}
        align="center"
        justify="center"
        paddingBottom={sizes.sm}>
        {/* Past Tab */}
        <TabButton index={0} icon={assets.document} label={t('home.past')} />
        {/* Divider */}
        <Block
          gray
          flex={0}
          width={1}
          marginHorizontal={sizes.sm}
          height={sizes.socialIconSize}
        />
        {/* Today Tab */}
        <TabButton index={1} icon={assets.calendar} label={t('home.today')} />
        {/* Divider */}
        <Block
          gray
          flex={0}
          width={1}
          marginHorizontal={sizes.sm}
          height={sizes.socialIconSize}
        />
        {/* Next Tab */}
        <TabButton index={2} icon={assets.components} label={t('home.next')} />
      </Block>

      {renderPaginationInfo()}
      {/* Appointments list with improved spacing */}
      <Block
        scroll
        paddingHorizontal={sizes.padding}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        contentContainerStyle={styles.scrollContent}>
        <Block style={styles.appointmentsContainer}>
          {isLoading && !pastLoading ? (
            <Block
              card
              padding={sizes.sm}
              center
              flex={1}
              style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </Block>
          ) : displayAppointments?.length > 0 ? (
            <Block>
              {displayAppointments?.map((appointment) => (
                <AppointmentCard
                  client_name={`${appointment?.customer_obj?.first_name} ${appointment?.customer_obj?.last_name}`}
                  therapist_name={`${appointment?.therapist_obj?.first_name} ${appointment?.therapist_obj?.last_name}`}
                  appointment_date={appointment?.date}
                  time={appointment?.time}
                  service_name={
                    appointment?.package_balance
                      ? appointment?.package_option_obj?.package?.name
                      : appointment?.appointment_services[0]?.service_name
                  }
                  service_duration={appointment?.total_duration}
                  // reward_points={appointment?.reward_points ?? 0}
                  location_name={
                    locations?.find(
                      (lo) => lo.location == appointment?.location,
                    )?.name ?? 'Location'
                  }
                  notes={appointment?.notes}
                  key={`card-${appointment?.id}`}
                  user="user"
                  status={appointment?.status}
                  total_price={appointment?.total_price}
                  sharedPackage={
                    appointment?.shared_package_obj?.package_option?.package
                      ?.name
                  }
                  userPackage={
                    appointment?.user_package_obj?.package_option?.package?.name
                  }
                  unlimitedPackage={
                    appointment?.unlimited_package_obj?.package_option?.package
                      ?.name
                  }
                  package_balance_obj={
                    appointment?.user_package_obj ||
                    appointment?.shared_package_obj ||
                    appointment?.unlimited_package_obj
                  }
                />
              ))}

              {/* Render loading indicator for pagination */}
              {renderPastLoadingIndicator()}
              {/* Render pagination component */}
              {renderPagination()}

              <Button
                primary
                marginTop={sizes.sm}
                marginBottom={sizes.xl}
                style={styles.bookButton}
                row
                align="center"
                justify="center"
                onPress={() => navigation.navigate('Bookings' as never)}>
                <Feather name="calendar" size={20} color={colors.white} />
                <Text white marginLeft={sizes.s}>
                  Book Appointment
                </Text>
              </Button>
            </Block>
          ) : (
            <Block padding={sizes.md} style={styles.emptyStateContainer}>
              <Text p color={colors.gray} center marginBottom={sizes.m}>
                {tab === 0
                  ? 'No past appointments found'
                  : tab === 1
                    ? 'No appointments for today'
                    : 'No upcoming appointments'}
              </Text>

              <Button
                primary
                style={styles.bookButton}
                row
                align="center"
                justify="center"
                onPress={() => navigation.navigate('Bookings' as never)}>
                <Feather name="calendar" size={20} color={colors.white} />
                <Text white marginLeft={sizes.s}>
                  Book now!
                </Text>
              </Button>
            </Block>
          )}
        </Block>
      </Block>
    </Block>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    paddingTop: Platform.OS === 'android' ? Constants.statusBarHeight : 0,
  },
  header: {
    paddingVertical: 3,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    marginBottom: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backButton: {
    position: 'absolute',
    left: 16,
    top: Platform.OS === 'android' ? 4 : 4,
    zIndex: 10,
    backgroundColor: 'transparent',
  },
  headerTitle: {
    textAlign: 'center',
    marginVertical: 8,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  appointmentsContainer: {
    marginTop: 8,
  },
  loadingContainer: {
    minHeight: 200,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  emptyStateContainer: {
    minHeight: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.01)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    marginTop: 12,
    paddingVertical: 24,
  },
  bookButton: {
    height: 48,
    paddingHorizontal: 14,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
});

export default UserBookingHistory;
