import React, {useCallback, useState, useEffect} from 'react';
import {ActivityIndicator, Platform, StyleSheet, View} from 'react-native';
import {useNavigation} from '@react-navigation/core';

import {useData, useTheme, useTranslation} from '../../hooks/';
import * as regex from '../../constants/regex';
import {Block, Button, Input, Text} from '../../components/';
import Toast from 'react-native-toast-message';
import axios from 'axios';
import {useAuth} from '../../context/AuthContext';
const isAndroid = Platform.OS === 'android';

interface ILogin {
  email: string;
}

const API_URL = process.env.EXPO_PUBLIC_SERVER_URI;
const NewLogin = () => {
  const {isDark} = useData();
  const {t} = useTranslation();
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [login, setLoginData] = useState<ILogin>({
    email: '',
  });
  const [isValid, setIsValid] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const {colors, gradients, sizes} = useTheme();

  const handleChange = useCallback((value: string) => {
    setLoginData((state) => ({...state, email: value}));
    setIsValid(value.trim() !== '' && regex.email.test(value));
  }, []);

  // Add effect to reset error when email changes
  useEffect(() => {
    if (error) setError(null);
  }, [login.email]);

  const handleLogin = async () => {
    if (!login.email.trim()) {
      setError('Email is required');
      return;
    }
    
    if (!isValid) {
      setError('Please enter a valid email');
      return;
    }
    
    setIsLoading(true);
    try {
      const url = `${API_URL}/auth/token/generate-otp/`;
      const res = await axios.post(url, {
        email: login.email,
      });

      Toast.show({
        type: 'success',
        text1: res.data.message ?? 'OTP generated successfully',
      });
      // OTP generated successfully, navigate to VerifyToken screen
      navigation.navigate('VerifyToken', {email: login.email, isSignUp: false});
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        setError(error.response.data.error || 'Failed to generate OTP');
        console.error('Error login:', error.response.data);
      } else {
        setError('Failed to generate OTP. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Block safe marginTop={sizes.md}>
      <Block paddingHorizontal={sizes.s}>
        <Block
          keyboard
          behavior={!isAndroid ? 'padding' : 'height'}
          marginTop={sizes.xl}
          marginBottom={sizes.xxl}>
          <Block
            flex={0}
            radius={sizes.sm}
            marginHorizontal="3%"
            marginTop="50%"
            shadow={!isAndroid}>
            <Block
              blur
              flex={0}
              intensity={90}
              radius={sizes.sm}
              overflow="hidden"
              justify="space-evenly"
              tint={colors.blurTint}
              paddingVertical={sizes.sm}>
              <Text h3 center bold>
                {t('common.signin')}
              </Text>
              <Block paddingHorizontal={sizes.sm}>
                <Input
                  autoCapitalize="none"
                  marginBottom={sizes.m}
                  label={t('common.email')}
                  keyboardType="email-address"
                  placeholder={t('common.emailPlaceholder')}
                  success={Boolean(login.email && isValid)}
                  danger={Boolean(login.email && !isValid)}
                  onChangeText={handleChange}
                />
              </Block>
              {error && (
                <Text color={colors.danger} center marginBottom={sizes.s}>
                  {error}
                </Text>
              )}
              <Button
                onPress={handleLogin}
                marginVertical={sizes.s}
                marginHorizontal={sizes.sm}
                gradient={gradients.primary}
                disabled={!isValid || login.email.trim() === ''}>
                {isLoading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#fff" />
                  </View>
                ) : (
                  <Text bold white transform="uppercase">
                    {t('common.getOTP')}
                  </Text>
                )}
              </Button>

              <Block
                row
                center
                align="center"
                marginVertical={sizes.s}
                marginHorizontal={sizes.sm}>
                <Text>Don't have account yet? </Text>
                <Text
                  bold
                  primary
                  transform="uppercase"
                  marginLeft={1}
                  onPress={() => navigation.navigate('Register')}>
                  {t('common.signup')}
                </Text>
              </Block>
            </Block>
          </Block>
        </Block>
      </Block>
    </Block>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
    backgroundColor: 'transparent',
  },
});

export default NewLogin;