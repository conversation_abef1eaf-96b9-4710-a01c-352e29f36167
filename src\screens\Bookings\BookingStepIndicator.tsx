import React from 'react';
import {StyleSheet, ViewStyle} from 'react-native';
import {Block, Text} from '../../components/';
import {useTheme} from '../../hooks/';
import {Feather} from '@expo/vector-icons';

interface BookingStepIndicatorProps {
  step: number;
  totalSteps?: number;
}

const BookingStepIndicator: React.FC<BookingStepIndicatorProps> = ({
  step,
  totalSteps = 5,
}) => {
  const {sizes, colors} = useTheme();

  const renderStep = (index: number) => {
    const isCompleted = index < step - 1;
    const isCurrent = index === step - 1;

    const stepStyle: ViewStyle = {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: isCompleted ? colors.primary : colors.gray,
      alignItems: 'center',
      justifyContent: 'center',
      //   marginHorizontal: sizes.xs,
    };

    if (isCurrent) {
      stepStyle.backgroundColor = colors.white;
      stepStyle.borderWidth = 2;
      stepStyle.borderColor = colors.primary;
    }

    return (
      <Block key={index} style={stepStyle} flex={0}>
        {isCompleted && <Feather name="check" size={16} color={colors.white} />}

        {!isCompleted && (
          <Text p color={isCurrent ? colors.primary : colors.white}>
            {index + 1}
          </Text>
        )}
      </Block>
    );
  };

  return (
    <Block
      card
      padding={sizes.s}
      paddingVertical={sizes.sm}
      marginBottom={sizes.m}
      style={styles.container}
      row
      align="center">
      <Block row align="center" justify="space-around">
        {Array.from({length: totalSteps}, (_, index) => renderStep(index))}
      </Block>
    </Block>
  );
};

const styles = StyleSheet.create({
  container: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});

export default BookingStepIndicator;
