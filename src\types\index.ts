import {Package_Obj} from './package_obj';

export interface IDuration {
  id: string;
  price: string;
  time: string | number;
}

export interface IServicesPayload {
  created_at: string;
  description: string;
  durations: IDuration[];
  id: string;
  name: string;
  uploaded_at: string;
}

export interface IPackagesPayload {
  benefits: string[];
  created_at: string;
  description: string;
  id: string | number;
  name: string;
  type?: string;
  options: IDuration[];
  services_included: IServicesPayload[];
  updated_at: string;
}

export interface ITherapistResponseData {
  first_name: string;
  gender_preference: string;
  id: string;
  last_name: string;
  full_name: string;
  qualifications: string;
  start_year?: string;
  user: string;
  gender: string;
  location: string;
}

export interface ITherapistData {
  id: string;
  name: string;
  image: any;
  qualifications: string;
  
}

export interface IAppointmentService {
  service_name: string; // Name of the service
  customer_name: string; // Full name of the customer
  service_duration: number; // Duration of the service in minutes
  service_price: number; // Price of the service
}

export interface IAppointment {
  appointment_services: IAppointmentService[]; // Replace `Record<string, any>` with a specific type if known
  created_at: string; // ISO timestamp
  customer: number;
  customer_obj: {
    address: string | null;
    date_of_birth: string | null; // ISO date or null
    email: string;
    first_name: string;
    id: number;
    last_name: string;
    phone_number: string;
    profile_picture: string | null;
    role: 'customer' | 'admin' | 'therapist'; // Add other roles if applicable
  };
  date: string; // ISO date
  id: number;
  location: string;
  notes: string | null;
  package_balance: number | null;
  package_balance_obj: Record<string, any> | null; // Replace `Record<string, any>` with a specific type if known
  package_option: string | null;
  package_option_obj: Record<string, any> | null; // Replace `Record<string, any>` with a specific type if known
  status: 'booked' | 'cancelled' | 'completed'; // Add other statuses if applicable
  therapist: number;
  therapist_obj: {
    first_name: string;
    gender_preference: 'M' | 'F' | 'B'; // Male, Female, Both
    id: number;
    last_name: string;
    qualifications: string;
    start_year: number | null;
    user: string; // Email of the therapist
    working_hours: Array<Record<string, any>>; // Replace `Record<string, any>` with a specific type if known
  };
  time: string; // Time in HH:mm:ss format
  total_duration: number; // Duration in minutes
  reward_points: number; // Duration in minutes
  total_price: number; // Price in the applicable currency
  updated_at: string; // ISO timestamp
  user_package_obj?: Package_Obj | null;
  user_package?: number | null;
  unlimited_package_obj?: Package_Obj | null;
  unlimited_package?: number | null;
  shared_package_obj?: Package_Obj | null;
  shared_package?: number | null;
}
