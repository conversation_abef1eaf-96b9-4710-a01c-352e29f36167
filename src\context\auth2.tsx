import React, {createContext, ReactNode, useEffect, useState} from 'react';
import axios from 'axios';
import * as SecureStore from 'expo-secure-store';

const API_URL = process.env.EXPO_PUBLIC_SERVER_URI;
export interface IUserData {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  role: string;
  address: string | null;
  date_of_birth: string | null;
  profile_picture: string | null;
  reward_balance?: {
    total_points: number;
  };
  rewards?: IRewards[];
}

export interface IRewards {
  id: number;
  points: number;
  reward_date: string;
  created_at: string;
}
interface AuthContextType {
  auth: {
    accessToken: string | null;
    refreshToken: string | null;
  };
  login: (email: string, otp: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  setUserData: (data: IUserData) => void;
  userData: IUserData | null;
}

const AuthContext = createContext<AuthContextType | null>(null);
interface AuthProviderProps {
  children: ReactNode;
}
export const NewAuthProvider = ({children}: AuthProviderProps) => {
  const [auth, setAuth] = useState({
    accessToken: '',
    refreshToken: '',
  });
  const [userData, setUserData] = useState<IUserData | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load tokens and user data on mount
  useEffect(() => {
    const loadStoredData = async () => {
      try {
        const accessToken = await SecureStore.getItemAsync('accessToken');
        const refreshToken = await SecureStore.getItemAsync('refreshToken');
        const userDataString = await SecureStore.getItemAsync('userData');

        setAuth({
          accessToken: accessToken || '',
          refreshToken: refreshToken || 'null',
        });

        if (userDataString) {
          setUserData(JSON.parse(userDataString));
        }
      } catch (error) {
        console.error('Error loading stored data:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    loadStoredData();
  }, []);

  const login = async (email: any, otp: any) => {
    try {
      const response = await axios.post(`${API_URL}/auth/token/`, {email, otp});
      console.log('response', response);
      if (!response.data.access || !response.data.refresh) {
        throw new Error('Invalid login response. Tokens missing.');
      }
      const accessToken = response.data.access;
      const refreshToken = response.data.refresh;

      // Update state
      setAuth({
        accessToken,
        refreshToken,
      });

      // Store tokens securely
      await SecureStore.setItemAsync('accessToken', accessToken);
      await SecureStore.setItemAsync('refreshToken', refreshToken);

      return accessToken; // Return access token for immediate use
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const refreshToken = async () => {
    try {
      // Get the latest refresh token from secure storage
      const storedRefreshToken = await SecureStore.getItemAsync('refreshToken');

      if (!storedRefreshToken) {
        console.error('No refresh token available');
        return null;
      }

      console.log('Refreshing token with stored refresh token');
      const response = await axios.post(`${API_URL}/auth/token/refresh/`, {
        refresh: storedRefreshToken,
      });

      console.log('Token refresh response:', response.data);

      if (!response.data.access) {
        throw new Error('Invalid refresh response. Access token missing.');
      }

      const newAccessToken = response.data.access;

      // Update state
      setAuth((prev) => ({...prev, accessToken: newAccessToken}));

      // Store new access token
      await SecureStore.setItemAsync('accessToken', newAccessToken);

      return newAccessToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await SecureStore.deleteItemAsync('accessToken');
      await SecureStore.deleteItemAsync('refreshToken');
      await SecureStore.deleteItemAsync('userData');

      setAuth({accessToken: '', refreshToken: ''});
      setUserData(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Don't render until we've loaded the stored data
  if (!isInitialized) {
    return null; // Or a loading indicator
  }

  return (
    <AuthContext.Provider
      value={{
        auth,
        login,
        refreshToken,
        setUserData,
        userData,
        logout,
      }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => React.useContext(AuthContext);
