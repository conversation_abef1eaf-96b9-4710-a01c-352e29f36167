import {useState, useEffect, useCallback} from 'react';
// import {axiosPrivate} from '../lib/use-axios-private';
import {useAuth} from '../context/AuthContext';
import useAxiosPrivate from '../lib/use-axios-private';

export type CommissionResponse = Record<string, unknown>;

export interface CommissionState<T> {
  commissions: T[];
  monthlyCommissions: string;
  last7DaysCommission: number;
  isLoading: boolean;
  error: string | null;
}

const useCommission = <T extends CommissionResponse>() => {
  const axiosPrivate = useAxiosPrivate();
  const [state, setState] = useState<CommissionState<T>>({
    commissions: [],
    monthlyCommissions: '0.00',
    last7DaysCommission: 0,
    isLoading: true,
    error: null,
  });

  const today = new Date();
  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(today.getDate() - 7);

  const fetchData = useCallback(async (endpoint: string): Promise<T[]> => {
    try {
      const response = await axiosPrivate.get<T[]>(`commission/${endpoint}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching data from ${endpoint}:`, error);
      throw new Error(
        error instanceof Error ? error.message : 'Unknown error occurred',
      );
    }
  }, []);

  useEffect(() => {
    const fetchAllCommissions = async () => {
      setState((prev) => ({...prev, isLoading: true, error: null}));

      try {
        const [commissions, monthlyCommissions] = await Promise.all([
          fetchData('commissions/'),
          fetchData('commissions/monthly/'),
        ]);

        const last7DaysCommission = commissions
          .filter((commission) => {
            const commissionDate = new Date(commission.date as string);
            return commissionDate >= sevenDaysAgo && commissionDate <= today;
          })
          .reduce(
            (total, commission) =>
              total + parseFloat((commission.amount as string) || '0'),
            0,
          );

        const monthlyTotal = monthlyCommissions
          .reduce(
            (sum, item) =>
              sum + parseFloat((item.total_commissions as string) || '0'),
            0,
          )
          .toFixed(2);

        setState({
          commissions,
          monthlyCommissions: monthlyTotal,
          last7DaysCommission,
          isLoading: false,
          error: null,
        });
      } catch (error) {
        if (error) {
          setState((prev) => ({
            ...prev,
            isLoading: false,
            // error:
            //   error instanceof Error ? error.message : 'Unknown error occurred',
          }));
        }

      }
    };
    fetchAllCommissions();
  }, []);

  return state;
};

export default useCommission;
