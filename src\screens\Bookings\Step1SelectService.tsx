import React, {useEffect, useState} from 'react';
import {Block, Text, Button} from '../../components';
import {useTheme} from '../../hooks';
import {BookingData} from './types';

import {
  StyleSheet,
  TouchableOpacity,
  View,
  Text as NativeText,
  ActivityIndicator,
} from 'react-native';
import Entypo from '@expo/vector-icons/Entypo';
import useServicesAndPackages from '../../hooks/useServicesAndPackages';
import {IDuration, IPackagesPayload, IServicesPayload} from '../../types';
import {useBooking} from '../../context/BookingContext';
import useGetActivePackage from '../../hooks/useGetActivePackage';
import RenderPackageOptions from './renderPackageOptions';

interface ServicePrice {
  time: number;
  price: number;
  durationId?: number | string;
}
const SelectService2 = () => {
  const {
    services,
    packages,
    isFetchingServices,
    isFetchingPackages,
    sharedPackages,
    unlimitedPackages,
  } = useServicesAndPackages();
  const {activePackage, activeSharedPackage, activeUnlimitedPackage} =
    useGetActivePackage();
  const {sizes, colors} = useTheme();
  const [currentMode, setCurrentMode] = useState<
    'individual' | 'package' | 'shared_packages' | 'unlimited_packages'
  >('individual');
  const {bookingData, updateBookingData} = useBooking();

  const [selectedPackage, setSelectedPackage] = useState<
    BookingData['package']
  >(bookingData.package || null);

  const [selectedServices, setSelectedServices] = useState<
    Record<string, ServicePrice>
  >(
    bookingData?.services?.reduce(
      (acc, service) => ({
        ...acc,
        [service.id]: {
          time: service.time,
          price: service.price,
          durationId: service.durationId,
        },
      }),
      {} as Record<string, ServicePrice>,
    ) || {},
  );

  useEffect(() => {
    // Initialize state from bookingData when the component mounts
    if (bookingData.package) {
      if (
        bookingData?.package?.type == 'package' ||
        bookingData?.package?.type == 'my-package'
      ) {
        setCurrentMode('package');
      }

      if (
        bookingData?.package?.type == 'shared_package' ||
        bookingData?.package?.type == 'my-shared_package'
      ) {
        setCurrentMode('shared_packages');
      }

      if (
        bookingData?.package?.type == 'unlimited_package' ||
        bookingData?.package?.type == 'my-unlimited_package'
      ) {
        setCurrentMode('unlimited_packages');
      }
    }
    if (bookingData.services && bookingData.services.length > 0) {
      setCurrentMode('individual');
    }
  }, [bookingData]);

  const handleServiceDurationSelect = (
    service: IServicesPayload,
    duration: IDuration,
  ) => {
    const isAlreadySelected =
      selectedServices[service.id]?.time === Number(duration.time);

    if (isAlreadySelected) {
      const updatedServices = {...selectedServices};
      delete updatedServices[service.id];
      setSelectedServices(updatedServices);
      updateBookingData(
        'services',
        Object.entries(updatedServices).map(([id, duration]) => ({
          id: Number(id),
          name: services.find((s) => s.id == id)?.name || '',
          time: duration.time,
          price: duration.price,
          durationId: duration?.durationId,
        })),
      );
    } else {
      const updatedServices = {
        ...selectedServices,
        [service.id]: {
          time: Number(duration.time), // Convert to number
          price: Number(duration.price), // Convert to number
          durationId: duration?.id,
        },
      };

      setSelectedServices(updatedServices);

      updateBookingData(
        'services',
        Object.entries(updatedServices).map(([id, service]) => {
          return {
            id: Number(id), // Convert id to a number
            name: services.find((s) => s.id == id)?.name || '',
            time: service.time, // Already a number
            price: service.price, // Already a number
            durationId: service.durationId,
          };
        }),
      );
      // Clear package selection
      setSelectedPackage(null);
      updateBookingData('package', null);
    }
  };

  const handlePackageSelect = (
    type: string,
    pkg: IPackagesPayload,
    option: IDuration,
  ) => {
    const isAlreadySelected =
      selectedPackage?.id === +pkg.id &&
      selectedPackage?.time === Number(option.time);

    if (isAlreadySelected) {
      setSelectedPackage(null);
      updateBookingData('package', null);
    } else {
      const packageSelection = {
        type,
        id: Number(pkg.id),
        name: pkg.name,
        time: Number(option.time),
        price: Number(option.price),
        durationId: option.id,
      };
      setSelectedPackage(packageSelection);
      updateBookingData('package', packageSelection);
      // Clear individual service selections
      setSelectedServices({});
      updateBookingData('services', []);
    }
  };

  const renderServiceOptions = () => (
    <>
      {isFetchingServices ? (
        <Block card padding={sizes.sm} center flex={1}>
          <ActivityIndicator size="large" color={colors.primary} />
        </Block>
      ) : (
        services.map((service) => (
          <Block
            card
            paddingVertical={25}
            style={{borderColor: colors.gray}}
            key={service.name}
            marginBottom={sizes.md}
            padding={sizes.text}>
            <Text h4 semibold marginBottom={sizes.xs} center>
              {service.name}
            </Text>
            <Text color={colors.black} p center marginBottom={sizes.s}>
              {service.description}
            </Text>

            <View style={styles.durationOptions}>
              {service.durations.map((duration) => {
                const isSelected =
                  selectedServices[service.id]?.time === Number(duration.time);

                return (
                  <TouchableOpacity
                    key={duration.time}
                    style={[
                      styles.durationButton,
                      isSelected
                        ? styles.selectedDurationButton
                        : styles.unselectedDurationButton,
                    ]}
                    onPress={() =>
                      handleServiceDurationSelect(service, duration)
                    }>
                    <NativeText
                      style={[
                        styles.durationText,
                        isSelected
                          ? styles.selectedDurationText
                          : styles.unselectedDurationText,
                      ]}>
                      {duration.time} min - {duration.price} AED
                    </NativeText>

                    {isSelected && (
                      <Entypo name="check" size={24} color="#32CD32" />
                    )}
                  </TouchableOpacity>
                );
              })}
            </View>
          </Block>
        ))
      )}
    </>
  );

  const renderActivePackageOptions = (type: string, pkg: IPackagesPayload) => (
    <Block
      key={pkg.name + pkg.id}
      card
      paddingVertical={25}
      style={{borderColor: colors.gray}}
      marginBottom={sizes.md}
      padding={sizes.text}>
      <Text h4 semibold marginBottom={sizes.xs} center>
        {pkg.name}
      </Text>
      <Text
        color={colors.black}
        size={sizes.text}
        p
        center
        marginBottom={sizes.s}>
        {pkg.description}
      </Text>
  
      <View style={styles.durationOptions}>
        {pkg.options.map((option) => {
          const isSelected =
            selectedPackage?.id === +pkg.id &&
            selectedPackage?.time === Number(option.time);
          
          // Check if this is an unlimited package type
          const isUnlimitedPackage = 
            type === 'unlimited_package' || 
            type === 'my_unlimited_package';
            
          return (
            <TouchableOpacity
              style={[
                styles.durationButton,
                isSelected
                  ? styles.selectedDurationButton
                  : styles.unselectedDurationButton,
              ]}
              onPress={() => handlePackageSelect(type, pkg, option)}
              key={option.time}>
              <NativeText
                style={[
                  styles.durationText,
                  isSelected
                    ? styles.selectedDurationText
                    : styles.unselectedDurationText,
                ]}>
                {isUnlimitedPackage ? 
                  'Unlimited Access' : 
                  `Remaining Time - ${option.time} min`}
              </NativeText>
  
              {isSelected && <Entypo name="check" size={24} color="#32CD32" />}
            </TouchableOpacity>
          );
        })}
      </View>
    </Block>
  );
  return (
    <Block>
      <Text h5 semibold marginBottom={sizes.s}>
        Select Your Service
      </Text>

      <Block marginBottom={sizes.md}>
        <Block row marginBottom={10}>
          <Button
            flex={1}
            onPress={() => setCurrentMode('individual')}
            color={currentMode === 'individual' ? colors.primary : colors.gray}>
            <Text white semibold>
              Individual Services
            </Text>
          </Button>

          <Button
            flex={1}
            onPress={() => setCurrentMode('package')}
            color={currentMode === 'package' ? colors.primary : colors.gray}
            marginLeft={sizes.s}>
            <Text white semibold>
              {activePackage ? 'Active Package' : 'Packages'}
              {/* {'Packages'} */}
            </Text>
          </Button>
        </Block>

        <Block row>
          <Button
            flex={1}
            onPress={() => setCurrentMode('shared_packages')}
            color={
              currentMode === 'shared_packages' ? colors.primary : colors.gray
            }>
            <Text white semibold>
              {activeSharedPackage
                ? 'Active Shared Package'
                : 'Shared Packages'}
            </Text>
          </Button>

          <Button
            flex={1}
            onPress={() => setCurrentMode('unlimited_packages')}
            color={
              currentMode === 'unlimited_packages'
                ? colors.primary
                : colors.gray
            }
            marginLeft={sizes.s}>
            <Text white semibold>
              {activeUnlimitedPackage
                ? 'Active Unlimited Package'
                : 'Unlimited Packages'}
            </Text>
          </Button>
        </Block>
      </Block>

      {currentMode === 'individual' ? (
        renderServiceOptions()
      ) : currentMode === 'package' ? (
        activePackage ? (
          renderActivePackageOptions('mypackage', {
            id: activePackage?.id,
            name: activePackage?.package_option?.package?.name,
            description: activePackage?.package_option?.package?.description,
            options: [
              {
                time: activePackage?.remaining_time,
                price: 0,
                id: activePackage?.package_option.id,
              },
            ],
          } as unknown as IPackagesPayload)
        ) : (
          <RenderPackageOptions
            packages={packages}
            isFetchingPackages={isFetchingPackages}
            selectedPackage={selectedPackage}
            handlePackageSelect={handlePackageSelect}
            type="package"
          />
        )
      ) : currentMode === 'shared_packages' ? (
        activeSharedPackage ? (
          renderActivePackageOptions('my_shared_package', {
            id: activeSharedPackage?.id,
            name: activeSharedPackage?.package_option?.package?.name,
            description:
              activeSharedPackage?.package_option?.package?.description,
            options: [
              {
                time: activeSharedPackage?.remaining_time,
                price: 0,
                id: activeSharedPackage?.package_option.id,
                type: 'my_shared_package',
              },
            ],
          } as unknown as IPackagesPayload)
        ) : (
          <RenderPackageOptions
            packages={sharedPackages}
            isFetchingPackages={isFetchingPackages}
            selectedPackage={selectedPackage}
            handlePackageSelect={handlePackageSelect}
            type="shared_package"
          />
        )
      ) : currentMode === 'unlimited_packages' ? (
        activeUnlimitedPackage ? (
          renderActivePackageOptions('my_unlimited_package', {
            id: activeUnlimitedPackage?.id,
            name: activeUnlimitedPackage?.package_option?.package?.name,
            description:
              activeUnlimitedPackage?.package_option?.package?.description,
            options: [
              {
                time: activeUnlimitedPackage?.remaining_time,
                price: 0,
                id: activeUnlimitedPackage?.package_option.id,
                type: 'my_unlimited_package',
              },
            ],
          } as unknown as IPackagesPayload)
        ) : (
          <RenderPackageOptions
            packages={unlimitedPackages}
            isFetchingPackages={isFetchingPackages}
            selectedPackage={selectedPackage}
            handlePackageSelect={handlePackageSelect}
            type="unlimited_package"
          />
        )
      ) : null}
    </Block>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16, // Matches `sizes.m`
  },
  card: {
    backgroundColor: '#FFFFFF', // Matches a light card color
    borderRadius: 8, // Matches `sizes.radius`
    padding: 16, // Matches `sizes.m`
    marginBottom: 16, // Matches `sizes.m`
    shadowColor: '#000000', // Shadow color
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
    flex: 1,
  },
  serviceInfo: {
    marginBottom: 8, // Matches `sizes.s`
  },
  serviceName: {
    fontSize: 18, // Matches `sizes.h5`
    fontWeight: 'bold',
    color: '#000000', // Black text for titles
  },
  serviceDescription: {
    fontSize: 14, // Matches `sizes.small`
    color: '#666666', // Gray text for descriptions
  },
  durationOptions: {
    marginTop: 20,
  },
  durationButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 8, // Matches `sizes.radius`
    padding: 12, // Matches `sizes.m`
    marginBottom: 12, // Matches `sizes.s`
  },
  selectedDurationButton: {
    backgroundColor: '#DFFFD6', // Light green background
    borderColor: '#32CD32', // Green border
    borderWidth: 1,
  },
  unselectedDurationButton: {
    backgroundColor: '#F5F5F5', // Light gray background
    borderColor: '#CCCCCC', // Light gray border
    borderWidth: 1,
  },
  durationText: {
    fontSize: 16, // Matches `sizes.medium`
    fontWeight: '500',
  },
  selectedDurationText: {
    color: '#32CD32', // Green text for selected
  },
  unselectedDurationText: {
    color: '#333333', // Dark gray text for unselected
  },
});

export default SelectService2;
