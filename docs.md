Absolutely. Here's a clean, practical `README.md` you can drop into your project to guide **EAS development** and **production builds** for both **Android** and **iOS**:

---

````markdown
# 📦 EAS Build Guide (Expo Application Services)

This guide covers how to build the app for **development** and **production** on both Android and iOS using EAS CLI.

---

## 📁 Requirements

- Node.js ≥ 16
- `eas-cli` installed globally
- Valid Apple Developer Account (for iOS)
- Android SDK or physical device (for Android)
- Apple device + macOS (for local iOS install or manual upload)

---

## 🔧 Development Build

Use this when testing features or running the app locally with `expo-dev-client`.

### Build the Development Version

```bash
eas build -p android --profile development
# or
eas build -p ios --profile development
```
````

### Start the Metro Bundler

```bash
npx expo start --dev-client
```

Use `a` to launch Android emulator or scan QR to launch on device.

---

## 🚀 Production Builds

These builds are for **publishing to the Play Store / App Store**.

### Android (.aab for Google Play)

```bash
eas build -p android --profile production
```

- Output: `.aab` file
- Use this for **Play Store uploads**

### iOS (.ipa for App Store)

```bash
eas build -p ios --profile production
```

- Output: `.ipa` file
- Use with TestFlight or App Store Connect

---

## 📤 Submit to Store

After production build is done:

### Submit to Google Play

Upload the `.aab` manually via Play Console.

### Submit to Apple (TestFlight or App Store)

```bash
eas submit --platform ios --profile production
```

---

## 🔁 Versioning & Auto-Increment

- `autoIncrement: true` is set in `eas.json` to bump version codes automatically.
- Update `version` in `app.json` / `app.config.js` as needed.

---

## 🧼 Helpful Commands

```bash
eas build:list               # View recent builds
eas build:run                # Install dev build to device (Android only)
eas device:create            # Register new iOS device for dev build
```

---

## 📦 eas.json Overview

Your `eas.json` should look like this:

```json
{
  "cli": {
    "version": ">= 14.4.0",
    "appVersionSource": "remote"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "env": {
        "EXPO_PUBLIC_SERVER_URI": "https://api.stretchup.ae"
      }
    },
    "preview": {
      "distribution": "internal",
      "autoIncrement": true,
      "env": {
        "EXPO_PUBLIC_SERVER_URI": "https://api.stretchup.ae"
      }
    },
    "production": {
      "distribution": "store",
      "autoIncrement": true,
      "env": {
        "EXPO_PUBLIC_SERVER_URI": "https://api.stretchup.ae"
      }
    }
  },
  "submit": {
    "production": {}
  }
}
```

---

## ✅ Notes

- For iOS builds, make sure Developer Mode is enabled on the device.
- Android builds require no device registration, just install via APK or QR.
- Use `TestFlight` for iOS testing if you don’t want to register each iOS device.

---

```

---

Let me know if you want it tailored more for your team or CI use (e.g. GitHub Actions).
```
