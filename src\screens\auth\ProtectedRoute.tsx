import React, {useEffect} from 'react';
import {ActivityIndicator, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {Text} from '../../components';
import {useAuth} from '../../context/AuthContext';
import BookingProvider from '../../context/BookingContext';
const ProtectedRoute = (WrappedComponent: React.ComponentType<any>) => {
  return (props: any) => {
    const authData = useAuth();
    const navigation = useNavigation();

    useEffect(() => {
      if (!authData?.auth?.accessToken || !authData?.userData) {
        navigation.navigate('Login' as never);
      }
    }, []);

    return (
      <BookingProvider>
        <WrappedComponent {...props} />
      </BookingProvider>
    );
  };
};

export default ProtectedRoute;
