import React, {createContext, useState, ReactNode} from 'react';
import {BookingData} from '../screens/Bookings/types';

interface BookingContextProps {
  bookingData: BookingData;
  updateBookingData: <K extends keyof BookingData>(
    key: K,
    value: BookingData[K],
  ) => void;
  setBookingData: (data: BookingData) => void;
}

const BookingContext = createContext<BookingContextProps>({
  bookingData: {
    services: null,
    package: null,
    location: null,
    therapist: null,
    serviceType: '',
    allocatedMinutes: '',
    date: '',
    time: '',
  },
  updateBookingData: () => {
    // Default implementation (no-op)
    // console.warn('updateBookingData is not implemented');
  },
  setBookingData: () => {},
});

const BookingProvider: React.FC<{children: ReactNode}> = ({children}) => {
  const [bookingData, setBookingData] = useState<BookingData>({
    services: [],
    package: null,
    location: null,
    therapist: null,
    allocatedMinutes: null,
    serviceType: '',
    date: null,
    time: null,
  });

  const updateBookingData = <K extends keyof BookingData>(
    key: K,
    value: BookingData[K],
  ) => {
    setBookingData((prevData) => ({...prevData, [key]: value}));
  };

  return (
    <BookingContext.Provider
      value={{bookingData, updateBookingData, setBookingData}}>
      {children}
    </BookingContext.Provider>
  );
};

export default BookingProvider;

export const useBooking = () => React.useContext(BookingContext);
