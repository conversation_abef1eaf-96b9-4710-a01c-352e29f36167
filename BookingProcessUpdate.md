# Documentation: Updating React Native Booking Process

## Overview

The latest requirements introduce support for multiple package types (`mypackage`, `shared_package`, `my_shared_package`, `unlimited_package`, `my_unlimited_package`) with specific API endpoints, payload structures, and logic. This document identifies what’s new, key changes, and necessary updates to align the code with these requirements as of March 21, 2025.

### Key Differences from Current Code

1. **Package Types**:
   - **Old**: Only supports individual services and generic packages.
   - **New**: Adds specific package types: user packages (`mypackage`), shared packages (`shared_package`, `my_shared_package`), and unlimited packages (`unlimited_package`, `my_unlimited_package`).
2. **API Endpoints**:
   - **Old**: Uses a single endpoint (`/appointment/appointments/`) for all bookings.
   - **New**: Differentiates endpoints based on package type (`/appointment/appointments/`, `/appointment/appointments/shared/`, `/appointment/appointments/unlimited/`).
3. **Duration Handling**:
   - **Old**: Fixed duration selection for packages.
   - **New**: Introduces dynamic allocation of minutes via a slider for packages, with `maxDuration` and `allocatedMinutes`.
4. **Active Package Logic**:
   - **Old**: No explicit handling of active packages.
   - **New**: Fetches and prioritizes active packages (`my_*`) with zero cost if used.
5. **State Management**:
   - **Old**: Limited `bookingData` structure.
   - **New**: Expands `bookingData` to include package-specific IDs and durations.

---

## What’s New

1. **Multiple Package Types**:
   - Users can book using their active packages (`mypackage`, `my_shared_package`, `my_unlimited_package`) or purchase new ones (`package`, `shared_package`, `unlimited_package`).
   - Each type has unique API endpoints and logic.
2. **Dynamic Duration Allocation**:
   - For packages, users allocate minutes from a total (`maxDuration`) using a slider, stored as `allocatedMinutes`.
3. **Active Package Integration**:
   - Fetch and display active packages with remaining time; bookings using these are free (`total_price: 0`).
4. **Expanded Payload**:
   - Includes package-specific IDs (`user_package_id`, `shared_package_id`, `unlimited_package_id`) and `serviceType` to differentiate booking types.
5. **API Enhancements**:
   - New endpoints for fetching active packages and submitting bookings by type.

---

## Component-by-Component Changes

### 1. `BookingFlow`

- **Current**: Manages a 6-step flow with basic validation.
- **Changes Needed**:
  - **State Expansion**: Update `bookingData` in `BookingContext` to include:
    ```javascript
    {
      serviceType: "service", // "mypackage", "shared_package", etc.
      user_package_id: null,
      shared_package_id: null,
      unlimited_package_id: null,
      maxDuration: 0,
      allocatedMinutes: 0
    }
    ```
  - **Validation**: Update `isStepComplete` to check `serviceType` and package-specific fields:
    ```javascript
    case 1:
      return (
        (bookingData.services?.length > 0 && bookingData.serviceType === "service") ||
        (bookingData.package && ["package", "shared_package", "unlimited_package"].includes(bookingData.serviceType)) ||
        (bookingData.user_package_id && bookingData.serviceType === "mypackage") ||
        (bookingData.shared_package_id && bookingData.serviceType === "my_shared_package") ||
        (bookingData.unlimited_package_id && bookingData.serviceType === "my_unlimited_package")
      );
    case 4:
      return (
        bookingData.date &&
        bookingData.time &&
        (!["mypackage", "my_shared_package", "shared_package"].includes(bookingData.serviceType) || bookingData.allocatedMinutes)
      );
    ```
  - **Navigation**: Ensure `handleNext` respects package-specific logic.

### 2. `SelectService2`

- **Current**: Supports individual services and generic packages with a toggle.
- **Changes Needed**:
  - **Fetch Active Packages**: Add API calls to fetch active packages:
    ```javascript
    const {myPackage, mySharedPackage, myUnlimitedPackage} =
      useGetActivePackages(); // Custom hook
    ```
  - **UI Update**: Replace the `individual/package` toggle with buttons for:
    - "Individual Services"
    - "My Package" (if `myPackage` exists)
    - "Shared Packages" (if `mySharedPackage` exists or purchasable)
    - "Unlimited Packages" (if `myUnlimitedPackage` exists or purchasable)
    - "New Packages" (for purchasable `package`)
  - **Logic**:
    - For active packages:
      ```javascript
      if (myPackage) {
        renderActivePackageOptions(myPackage, 'mypackage');
      }
      if (mySharedPackage) {
        renderActivePackageOptions(mySharedPackage, 'my_shared_package');
      }
      if (myUnlimitedPackage) {
        renderActivePackageOptions(myUnlimitedPackage, 'my_unlimited_package');
      }
      ```
    - Update `handlePackageSelect`:
      ```javascript
      const handlePackageSelect = (pkg, option, type) => {
        const packageSelection = {
          id: Number(pkg.id),
          name: pkg.name,
          time: Number(option.time),
          price: type.includes('my_') ? 0 : Number(option.price),
          durationId: option.id,
        };
        setSelectedPackage(packageSelection);
        updateBookingData('package', packageSelection);
        updateBookingData('serviceType', type);
        updateBookingData('maxDuration', Number(option.time));
        updateBookingData(
          type === 'mypackage'
            ? 'user_package_id'
            : type === 'shared_package' || type === 'my_shared_package'
              ? 'shared_package_id'
              : 'unlimited_package_id',
          pkg.id,
        );
        setSelectedServices({});
        updateBookingData('services', []);
      };
      ```
  - **API**: Fetch purchasable packages from `/service/packages/`, `/service/shared_package/`, `/service/unlimited_package/`.

### 3. `SelectLocation`

- **Current**: Static location selection.
- **Changes Needed**: No major changes

### 4. `SelectTherapist`

- **Current**: Fetches therapists and filters by location.
- **Changes Needed**:
  - **Filter Logic**: Already filters by `bookingData.location.location`; ensure compatibility with package bookings (no service-specific filtering needed).
  - **API**: Confirm `/staff/therapists-p/` returns all therapists for package bookings.

### 5. `SelectDateTime`

- **Current**: Supports date/time selection with a slider for package minutes.
- **Changes Needed**:
  - **Slider Logic**: Update to use `maxDuration` from `bookingData`:
    ```javascript
    <CustomSlider
      maxValue={bookingData.maxDuration}
      onValueChange={(time) => handleAllocatedMinutesSelect(`${time}`)}
    />
    ```
  - **API Hook**: Update `useGetAllocatedTime` to pass `bookingData.serviceType` and package IDs to `/appointment/available-times/`.
  - **Validation**: Ensure `allocatedMinutes` is required for `mypackage`, `shared_package`, and `my_shared_package`.

### 6. `BookingSummary`

- **Current**: Submits to a single endpoint with basic payload.
- **Changes Needed**:
  - **Payload**: Update `handleConfirmBooking` to support package types:
    ```javascript
    const payload = {
      services: isPackageSelected
        ? [
            {
              id: bookingData.package.durationId,
              name: bookingData.package.name,
              time: Number(
                bookingData.allocatedMinutes || bookingData.package.time,
              ),
              price: bookingData.package.price,
              type: bookingData.serviceType,
            },
          ]
        : bookingData.services.map((service) => ({
            ...service,
            type: 'service',
          })),
      serviceType: bookingData.serviceType,
      ...(bookingData.serviceType === 'mypackage' && {
        user_package_id: bookingData.user_package_id,
      }),
      ...(bookingData.serviceType === 'shared_package' ||
        (bookingData.serviceType === 'my_shared_package' && {
          shared_package_id: bookingData.shared_package_id,
        })),
      ...(bookingData.serviceType === 'unlimited_package' ||
        (bookingData.serviceType === 'my_unlimited_package' && {
          unlimited_package_id: bookingData.unlimited_package_id,
        })),
      therapist_id: bookingData.therapist.id,
      date: bookingData.date,
      time: bookingData.time,
      duration: isPackageSelected
        ? Number(bookingData.allocatedMinutes)
        : totalDuration,
      total_price: totalPrice,
      location: bookingData.location.location,
      total_duration: totalDuration,
    };
    ```
  - **Endpoint**: Dynamically select based on `serviceType`:
    ```javascript
    const endpoint =
      bookingData.serviceType === 'service' ||
      bookingData.serviceType === 'mypackage' ||
      bookingData.serviceType === 'package'
        ? '/appointment/appointments/'
        : bookingData.serviceType.includes('shared')
          ? '/appointment/appointments/shared/'
          : '/appointment/appointments/unlimited/';
    await axiosPrivate.post(endpoint, payload);
    ```
  - **UI**: Display package type and remaining time if applicable.

### 7. `BookingConfirmation`

- **Current**: Displays basic confirmation.
- **Changes Needed**:
  - **UI**: Show package type and remaining time (e.g., "My Package - 120 mins remaining").
  - **Logic**: No backend changes needed; update `calculateTotal` to respect `price: 0` for active packages.

### 8. `BookingStepIndicator` and `CancellationPolicy`

- **Current**: Static UI components.
- **Changes Needed**: No functional changes; ensure styling aligns with new package flow.

---

## Additional Updates

1. **BookingContext**:
   - Expand `BookingData` type:
     ```typescript
     interface BookingData {
       serviceType: string;
       user_package_id?: string | null;
       shared_package_id?: string | null;
       unlimited_package_id?: string | null;
       maxDuration?: number;
       allocatedMinutes?: number;
       // Existing fields...
     }
     ```
2. **Custom Hooks**:
   - Create `useGetActivePackages` to fetch:
     - `/appointment/active-package/`
     - `/appointment/active-shared-package/`
     - `/appointment/active-unlimited-package/`
3. **Error Handling**:
   - Add checks for active package conflicts (e.g., prompt if a matching active package exists).

---

## Key Changes Summary

| **Aspect**        | **Old Implementation**     | **New Implementation**                                                            |
| ----------------- | -------------------------- | --------------------------------------------------------------------------------- |
| Package Types     | Generic packages only      | Specific types (`mypackage`, `shared_package`, etc.) with active/new distinctions |
| API Endpoints     | Single endpoint            | Multiple endpoints by package type                                                |
| Duration Handling | Fixed durations            | Dynamic `allocatedMinutes` via slider, capped by `maxDuration`                    |
| Active Packages   | Not supported              | Fetch and prioritize active packages with zero cost                               |
| Payload Structure | Basic service/package data | Includes `serviceType` and package-specific IDs                                   |

---

## Conclusion

must update the booking flow to handle multiple package types, integrate active package logic, and adjust API interactions.
