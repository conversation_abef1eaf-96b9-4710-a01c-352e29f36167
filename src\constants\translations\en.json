{"common": {"appDetails": "App Details", "appName": "App name", "appVersion": "App version", "buildVersion": "Build version", "expoVersion": "Expo version", "about": "About", "visit": "Visit", "posted": "Posted on {{date}}", "recommended": "Recommended for you", "viewall": "View All", "more": "More", "follow": "Follow", "followed": "Followed", "search": "Search", "night": "night", "day": "day", "booknow": "Book Now", "selectDate": "Select date", "checkInOut": "Check-In & Check-Out", "adults": "Adults", "roomType": "Room Type", "luxury": "Luxury", "message": "Enter your message", "album": "Album", "or": "or", "name": "Name", "namePlaceholder": "Enter your full name", "email": "Email", "emailPlaceholder": "Enter your email address", "password": "Password", "passwordPlaceholder": "Enter a password", "agree": "I agree with the ", "terms": "Terms and Conditions", "signup": "Sign Up", "signin": "Sign In", "goBack": "Go back", "inStock": "In stock", "outStock": "Out of stock", "addToCart": "Add to cart", "checkout": "Proceed to checkout", "readArticle": "Read Article", "extraNotes": "Extra Notes", "firstName": "First Name", "firstNamePlaceholder": "Enter your first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter your last name", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter your phone number", "getOTP": "Get OTP", "verifySignUp": "Verify Sign Up", "verifyLogin": "<PERSON><PERSON><PERSON>", "otp": "OTP", "otpPlaceholder": "Enter your OTP", "verify": "Verify", "enterOTP": "Enter the 6-digit OTP sent to your email", "send": "Send", "guests": {"one": "1 guest", "other": "{{count}} guests", "zero": "0 Guests"}}, "errors": {"invalidOTP": "Invalid OTP. Please try again."}, "app": {"name": "StretchUp", "fullname": "Soft UI Kit", "native": "React Native", "link": "creative-tim.com"}, "darkMode": "Dark Mode", "screens": {"home": "Home", "appointments": "Appointments", "pointsAndRank": "Points And Rank", "components": "Components", "articles": "Articles", "rental": "Rental", "profile": "Profile", "settings": "Settings", "register": "Create account", "extra": "Extra - Automotive"}, "navigation": {"home": "StretchUp", "appointments": "Appointments", "pointsAndRank": "Points And Rank", "components": "Components", "articles": "Articles", "rentals": "Rentals", "rental": "Best Deal", "booking": "Booking", "chat": "Cha<PERSON>", "profile": "Profile", "settings": "Settings", "notifications": "Notifications", "agreement": "User Agreement", "about": "About", "privacy": "Privacy", "register": "Register", "login": "<PERSON><PERSON>", "extra": "Automotive", "shopping": "Shopping Cart"}, "menu": {"started": "Getting Started", "documentation": "Documentation"}, "home": {"title": "Soft UI", "following": "Following", "trending": "Trending", "next": "Next", "past": "Past", "all": "All", "today": "Today", "appointments": "Appointments"}, "userHome": {"nextAppointment": "Next Appointment", "featuredService": "Featured Service", "seeAll": "See all"}, "extras": {"title1": "Want to try before", "title2": "you buy?", "description": "For a start, it does not automatically follow that a record amount of ice will melt this summer.", "schedule": "Schedule a test drive:", "available": "Available", "unavailable": "Unavailable", "save": "Save for later", "saved": "Saved", "book": "Book", "booked": "Booked", "contactUs": "Contact us"}, "register": {"title": "Welcome", "subtitle": "Register with"}, "login": {"title": "Welcome", "subtitle": "Sign In with"}, "settings": {"recommended": {"title": "Recommended Settings", "subtitle": "These are the most important settings", "faceid": "Use FaceID to sign in", "autolock": "Auto-Lock security", "notifications": "Notifications", "darkmode": "Dark Mode", "language": "Language"}, "payment": {"title": "Payment Settings", "subtitle": "These are the payment settings", "options": "Manage Payment Options", "giftcards": "Manage Gift Cards"}, "privacy": {"title": "Privacy Settings", "subtitle": "Third most important settings", "agreement": "User Agreement", "privacy": "Privacy", "about": "About"}, "notifications": {"title": "Notifications Settings", "subtitle": "These are the most important settings", "mentions": "Someone mentions me", "follows": "Anyone follows me", "comments": "Someone comments me", "offers": "New Offers"}}, "rentals": {"title": "Soft UI", "flight": "Flight", "hotel": "Hotel", "train": "Train", "availability": "Check availability", "interested": "You may also be interested in", "viewoffer": "View Offer", "notFound1": "We didn’t find ", "notFound2": " in our app.", "moreOptions": "You can see more options from other categories."}, "profile": {"title": "Profile", "posts": "Posts", "followers": "Followers", "following": "Following", "aboutMe": "About me"}, "notifications": {"title": "Notifications", "read": "Read Notifications", "unread": "Unread Notifications", "personal": "Personal", "business": "Business"}, "shop": {"subtotal": {"one": "Cart subtotal (1 item)", "other": "Cart subtotal ({{count}} items)", "zero": "Cart subtotal (0 items)"}, "alsoShopped": "Customers who shopped for items in your cart also shopped for"}}