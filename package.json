{"main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "start-a": "expo start -c", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "lint": "eslint . --ext .ts,.tsx --quiet"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "6.7.2", "@react-navigation/native": "6.1.18", "@react-navigation/stack": "6.4.1", "axios": "^1.7.9", "date-fns": "^4.1.0", "dayjs": "1.11.13", "expo": "^53.0.9", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.1", "expo-device": "^7.1.4", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.4", "expo-localization": "~16.1.5", "expo-notifications": "~0.31.2", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "i18n-js": "^4.4.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-calendars": "1.1306.0", "react-native-chart-kit": "^6.12.0", "react-native-confirmation-code-field": "^7.4.0", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-gifted-chat": "2.6.2", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-toast-message": "^2.2.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@react-native-community/eslint-config": "3.2.0", "@types/i18n-js": "3.8.9", "@types/react": "~19.0.10", "@types/react-native-calendars": "1.1264.7", "eslint": "^8.57.1", "jest-expo": "~53.0.5", "typescript": "~5.8.3"}, "private": true, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!(jest-)?react-native|react-clone-referenced-element|@react-native-community|expo(nent)?|@expo(nent)?/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|@sentry/.*)"]}}