import React from 'react';
import {StyleSheet} from 'react-native';
import {Block, Text, Button, Image} from '../../components/';
import {useTheme} from '../../hooks/';
import {Location, BookingData} from './types';
import {Feather} from '@expo/vector-icons';
import {useBooking} from '../../context/BookingContext';

const SelectLocation = () => {
  const {bookingData, updateBookingData} = useBooking();
  const {sizes, colors, assets} = useTheme();

  const locations: Location[] = [
    {
      id: 1,
      name: 'Studio Al Warqa Mall',
      address: 'Al Warqa Mall, Tripoli st, Dubai',
      location: 'A',
      image: assets.gym1,
    },
    {
      id: 2,
      name: 'Studio Al mizhar branch',
      address: 'Al mizhar branch (inside fithub), Dubai',
      location: 'B',
      image: assets.gym2,
    },
  ];

  const handleSelectLocation = (location: Location) => {
    updateBookingData('location', location);
  };

  return (
    <Block>
      <Text h5 semibold marginBottom={sizes.m}>
        Choose Your Preferred Location
      </Text>
      {locations.map((location) => {
        const isSelected = bookingData.location?.id === location.id;
        return (
          <Block key={location.id} card padding={0} marginBottom={sizes.m}>
            <Button
              onPress={() => handleSelectLocation(location)}
              color={isSelected ? colors.primary : colors.card}
              padding={0}
              height={200}
              style={styles.locationButton}>
              <Image
                source={location.image}
                style={[
                  styles.locationImage,
                  {
                    borderTopLeftRadius: sizes.cardRadius,
                    borderTopRightRadius: sizes.cardRadius,
                  },
                ]}
                background>
                <Block
                  color={'rgba(0,0,0,0.4)'}
                  position="absolute"
                  top={0}
                  left={0}
                  right={0}
                  bottom={0}
                />
                <Block padding={sizes.sm} justify="flex-end">
                  <Text
                    h5
                    bold={isSelected}
                    semibold={!isSelected}
                    color={isSelected ? colors.primary : colors.white}>
                    {location.name}
                  </Text>
                  <Block flex={0} align="center" row>
                    <Feather
                      name="map-pin"
                      size={16}
                      color={isSelected ? colors.white : colors.white}
                      style={styles.icon}
                    />
                    <Text
                      p
                      color={isSelected ? colors.white : colors.white}
                      semibold={isSelected}>
                      {location.address}
                    </Text>
                  </Block>
                </Block>
                {isSelected && (
                  <Block style={styles.checkmarkContainer}>
                    <Feather
                      name="check-circle"
                      size={24}
                      color={colors.primary}
                    />
                  </Block>
                )}
              </Image>
            </Button>
          </Block>
        );
      })}
    </Block>
  );
};

const styles = StyleSheet.create({
  locationButton: {
    overflow: 'hidden',
  },
  locationImage: {
    width: '100%',
    height: '100%',
  },
  icon: {
    marginRight: 8,
  },
  checkmarkContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    padding: 2,
  },
});

export default SelectLocation;
