export type AppointmentResponse = Record<string, unknown>;

export interface AppointmentState<T> {
  isLoading: boolean;
  error: string | null;
}

import {useState, useEffect, useCallback} from 'react';

import {IAppointment} from '../types';
import useAxiosPrivate from '../lib/use-axios-private';

export interface PaginatedAppointments {
  count: number;
  next: string | null;
  previous: string | null;
  results: IAppointment[];
}

const useAppointments = <T extends AppointmentResponse>() => {
  const axiosPrivate = useAxiosPrivate();
  const [state, setState] = useState<AppointmentState<T>>({
    isLoading: false,
    error: null,
  });

  const [pastLoading, setPastLoading] = useState<boolean>(false);
  // Track current page for past appointments
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  // Track items per page (determined dynamically)
  const [itemsPerPage, setItemsPerPage] = useState<number>(0);
  const [appointments, setAppointments] = useState<IAppointment[]>([]);
  const [pastAppointments, setPastAppointments] =
    useState<PaginatedAppointments>({
      count: 0,
      next: null,
      previous: null,
      results: [],
    });
  const [todayAppointments, setTodayAppointments] = useState<IAppointment[]>(
    [],
  );
  const [nextAppointments, setNextAppointments] = useState<IAppointment[]>([]);
  const [activePackage, setActivePackage] = useState<IAppointment>();
  const [activeTab, setActiveTab] = useState<'past' | 'today' | 'next'>(
    'today',
  );

  const fetchData = useCallback(
    async (endpoint: string): Promise<any> => {
      const response = await axiosPrivate.get<any>(`appointment/${endpoint}`);
      return response.data;
    },
    [axiosPrivate],
  );

  const extractPageNumber = useCallback((url: string | null): number => {
    if (!url) return 1;
    try {
      const urlObj = new URL(url);
      const pageParam = urlObj.searchParams.get('page');
      return pageParam ? Number.parseInt(pageParam, 10) : 1;
    } catch (error) {
      return 1;
    }
  }, []);
  // Calculate total pages based on count and items per page
  const calculateTotalPages = useCallback(
    (count: number, perPage: number): number => {
      if (perPage <= 0) return 1;
      return Math.ceil(count / perPage);
    },
    [],
  );
  // Fetch past appointments with page parameter
  const fetchPastAppointments = useCallback(
    async (page = 1): Promise<void> => {
      setPastLoading(true);
      try {
        const endpoint = `appointments-mobile/past/?page=${page}`;
        const data = await fetchData(endpoint);
        // Determine items per page based on results length
        // Only update if we're on page 1 or if it hasn't been set yet
        if (page === 1 || itemsPerPage === 0) {
          const newItemsPerPage = data.results.length;
          setItemsPerPage(newItemsPerPage);
          // Update total pages based on the newly determined items per page
          setTotalPages(calculateTotalPages(data.count, newItemsPerPage));
        }
        setPastAppointments(data);
        setCurrentPage(page);
        setPastLoading(false);
      } catch (error) {
        setPastLoading(false);
        setState((prev) => ({
          ...prev,
          error: 'Failed to fetch past appointments',
        }));
      }
    },
    [fetchData, calculateTotalPages, itemsPerPage],
  );
  // Navigate to specific page
  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        fetchPastAppointments(page);
      }
    },
    [fetchPastAppointments, totalPages],
  );
  // Navigate to next page
  const goToNextPage = useCallback(() => {
    if (pastAppointments.next) {
      const nextPage = extractPageNumber(pastAppointments.next);
      fetchPastAppointments(nextPage);
    }
  }, [pastAppointments.next, extractPageNumber, fetchPastAppointments]);
  // Navigate to previous page
  const goToPreviousPage = useCallback(() => {
    if (pastAppointments.previous) {
      const prevPage = extractPageNumber(pastAppointments.previous);
      fetchPastAppointments(prevPage);
    }
  }, [pastAppointments.previous, extractPageNumber, fetchPastAppointments]);

  const fetchAllAppointments = async () => {
    setState((prev) => ({...prev, isLoading: true, error: null}));

    try {
      // Fetch all appointments for backward compatibility
      const allAppointments = await fetchData('appointments/');
      setAppointments(allAppointments);

      const foundAppointment = allAppointments.find(
        (appointment: {package_balance_obj: {remaining_time: number}}) =>
          appointment?.package_balance_obj?.remaining_time > 19,
      );
      setActivePackage(foundAppointment);
      // Fetch today and next appointments
      const [today, next] = await Promise.all([
        fetchData('appointments-mobile/today/'),
        fetchData('appointments-mobile/next/'),
      ]);
      // Fetch past appointments with pagination
      await fetchPastAppointments(1);
      setTodayAppointments(today);
      setNextAppointments(next);
      setState({
        isLoading: false,
        error: null,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: 'Unknown error',
      }));
    }
  };
  useEffect(() => {
    fetchAllAppointments();
  }, [fetchData, fetchPastAppointments]);

  return {
    ...state,
    appointments,
    pastAppointments,
    todayAppointments,
    nextAppointments,
    activePackage,
    activeTab,
    setActiveTab,
    pastLoading,
    currentPage,
    totalPages,
    itemsPerPage,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    refetch: fetchAllAppointments,
  };
};

export default useAppointments;
