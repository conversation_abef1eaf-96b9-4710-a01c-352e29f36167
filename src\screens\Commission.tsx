import React, {useState, useCallback} from 'react';
import {
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Platform,
} from 'react-native';
import {Block, Button, Text} from '../components';
import {useTheme} from '../hooks';
import {format} from 'date-fns';
import useCommission from '../hooks/useCommission';
import Constants from 'expo-constants';
interface Commission {
  id: number;
  therapist_name: string;
  customer_name: string;
  date: string;
  amount: string;
}

const CommissionScreen = () => {
  const {colors, sizes, gradients} = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const {commissions, monthlyCommissions, last7DaysCommission, isLoading} =
    useCommission();

  // Sample data for the chart
  // const chartData = {
  //   labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  //   datasets: [
  //     {
  //       data: [350, 450, 280, 500, 600, 450],
  //     },
  //   ],
  // };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    // Fetch your commission data here
    setTimeout(() => setRefreshing(false), 2000);
  }, []);

  const renderCommissionItem = ({item}: {item: Commission}) => (
    <Block card marginBottom={8} padding={sizes.sm}>
      <Block row justify="space-between" marginBottom={sizes.xs}>
        <Text p semibold>
          {item.customer_name}
        </Text>
        <Text p bold color={colors.primary}>{`${item.amount} AED`}</Text>
      </Block>
      <Block row justify="space-between">
        <Text p color={colors.gray}>
          {format(new Date(item.date), 'MMM dd, yyyy')}
        </Text>
      </Block>
    </Block>
  );

  return (
    <Block
      safe
      marginBottom={100}
      style={{
        paddingTop:
          Platform.OS === 'android' ? Constants.statusBarHeight + 30 : 30,
      }}>
      <Block
        scroll
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        {/* Weekly and Monthly Overview */}
        <Block row padding={sizes.padding}>
          <Block
            card
            gradient={gradients.primary}
            marginRight={sizes.sm}
            padding={sizes.base}
            flex={1}>
            <Text h4 white>
              {last7DaysCommission} AED
            </Text>
            <Text p white>
              This Week
            </Text>
          </Block>
          <Block
            card
            gradient={gradients.secondary}
            padding={sizes.base}
            flex={1}>
            <Text h4 white>
              {monthlyCommissions} AED
            </Text>
            <Text p white>
              This Month
            </Text>
          </Block>
        </Block>

        <Block padding={sizes.sm}>
          <Text h5 semibold marginBottom={sizes.sm}>
            Recent Commissions
          </Text>
          <Block>
            {isLoading ? (
              <Block card padding={sizes.sm} center flex={1}>
                <ActivityIndicator size="large" color={colors.primary} />
              </Block>
            ) : (
              <FlatList
                data={
                  commissions.slice(
                    (currentPage - 1) * itemsPerPage,
                    currentPage * itemsPerPage,
                  ) as unknown as Commission[]
                }
                renderItem={renderCommissionItem}
                keyExtractor={(item) => item.id.toString()}
                showsVerticalScrollIndicator={false}
                scrollEnabled={false}
                ListEmptyComponent={
                  <Block card padding={sizes.sm} align="center">
                    <Text p color={colors.gray}>
                      No commissions to display
                    </Text>
                  </Block>
                }
                ListFooterComponent={
                  commissions.length > 0 ? (
                    <Block
                      row
                      justify="space-between"
                      marginTop={8}
                      align="center">
                      <Block row>
                        <Text p color={colors.gray}>
                          Showing{' '}
                          {Math.min(
                            (currentPage - 1) * itemsPerPage + 1,
                            commissions.length,
                          )}{' '}
                          -{' '}
                          {Math.min(
                            currentPage * itemsPerPage,
                            commissions.length,
                          )}{' '}
                          of {commissions.length}
                        </Text>
                      </Block>
                      <Block row flex={0}>
                        <Button
                          // card
                          marginRight={sizes.xs}
                          padding={sizes.xs}
                          disabled={currentPage === 1}
                          style={{opacity: currentPage === 1 ? 0.5 : 1}}
                          // opacity={currentPage === 1 ? 0.5 : 1}
                          onPress={() =>
                            setCurrentPage((prev) => Math.max(1, prev - 1))
                          }>
                          <Text p semibold color={colors.primary}>
                            Previous
                          </Text>
                        </Button>
                        <Button
                          padding={sizes.xs}
                          disabled={
                            currentPage * itemsPerPage >= commissions.length
                          }
                          style={{
                            opacity:
                              currentPage * itemsPerPage >= commissions.length
                                ? 0.5
                                : 1,
                          }}
                          onPress={() =>
                            setCurrentPage((prev) =>
                              Math.min(
                                Math.ceil(commissions.length / itemsPerPage),
                                prev + 1,
                              ),
                            )
                          }>
                          <Text p semibold color={colors.primary}>
                            Next
                          </Text>
                        </Button>
                      </Block>
                    </Block>
                  ) : null
                }
              />
            )}
          </Block>
        </Block>

        {/* Chart Section */}
        {/* <Block card margin={sizes.sm} padding={sizes.sm}>
          <Text h5 semibold marginBottom={sizes.sm}>
            Last 6 Months
          </Text>
          <BarChart
            data={chartData}
            width={Dimensions.get('window').width - sizes.padding * 4}
            height={220}
            yAxisLabel="AED "
            yAxisSuffix="AED "
            chartConfig={{
              backgroundColor: colors.white as string,
              backgroundGradientFrom: colors.white as string,
              backgroundGradientTo: colors.white as string,
              decimalPlaces: 0,
              color: (opacity = 1) => colors.primary as string,
              labelColor: (opacity = 1) => colors.text as string,
              style: {
                borderRadius: sizes.radius,
              },
              propsForBackgroundLines: {
                strokeDasharray: '',
                strokeWidth: 1,
                stroke: colors.gray,
                opacity: 0.1,
              },
            }}
            style={{
              marginVertical: sizes.sm,
              borderRadius: sizes.radius,
            }}
          />
        </Block> */}
      </Block>
    </Block>
  );
};

export default CommissionScreen;
