import {useState, useEffect, useCallback} from 'react';

import {IPackagesPayload, IServicesPayload} from '../types';
import useAxiosPrivate from '../lib/use-axios-private';

export type ServicesAndPackagesResponse = Record<string, unknown>;

const useServicesAndPackages = <T extends ServicesAndPackagesResponse>() => {
  const axiosPrivate = useAxiosPrivate();
  const [services, setServices] = useState<IServicesPayload[]>([]);
  const [packages, setPackages] = useState<IPackagesPayload[]>([]);
  const [sharedPackages, setSharedPackages] = useState<IPackagesPayload[]>([]);
  const [unlimitedPackages, setUnlimitedPackages] = useState<
    IPackagesPayload[]
  >([]);
  const [isFetchingServices, setIsFetchingServices] = useState<boolean>(false);
  const [isFetchingPackages, setIsFetchingPackages] = useState<boolean>(false);

  const fetchData = useCallback(
    async (
      endpoint: string,
    ): Promise<IServicesPayload[] | IPackagesPayload[]> => {
      try {
        const response = await axiosPrivate.get<
          IServicesPayload[] | IPackagesPayload[]
        >(`${endpoint}`);
        return response.data;
      } catch (error) {
        console.error(`Error fetching data from ${endpoint}:`, error);
        throw error; // Allow the calling function to handle the error
      }
    },
    [],
  );

  const fetchServices = useCallback(async () => {
    setIsFetchingServices(true);
    try {
      const data = await fetchData('/service/services/');
      setServices(data as IServicesPayload[]);
    } catch (error) {
      console.error('Failed to fetch services:', error);
    } finally {
      setIsFetchingServices(false);
    }
  }, []);

  const fetchPackages = useCallback(async () => {
    setIsFetchingPackages(true);
    try {
      const data = await fetchData('/service/packages/');
      setPackages(data as IPackagesPayload[]);
    } catch (error) {
      console.error('Failed to fetch packages:', error);
    } finally {
      setIsFetchingPackages(false);
    }
  }, []);

  const fetchSharedPackages = useCallback(async () => {
    try {
      const data = await fetchData('/service/shared_package/');
      setSharedPackages(data as IPackagesPayload[]);
    } catch (error) {
      console.error('Failed to fetch shared_package:', error);
    } finally {
    }
  }, []);

  const fetchUnlimitedPackages = useCallback(async () => {
    // setIsFetchingPackages(true);
    try {
      const data = await fetchData('/service/unlimited_package/');
      setUnlimitedPackages(data as IPackagesPayload[]);
    } catch (error) {
      console.error('Failed to fetch unlimited_package:', error);
    } finally {
    }
  }, []);

  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  useEffect(() => {
    fetchSharedPackages();
  }, [fetchSharedPackages]);
  useEffect(() => {
    fetchUnlimitedPackages();
  }, [fetchUnlimitedPackages]);

  return {
    services,
    packages,
    sharedPackages,
    unlimitedPackages,
    isFetchingServices,
    isFetchingPackages,
  };
};

export default useServicesAndPackages;
