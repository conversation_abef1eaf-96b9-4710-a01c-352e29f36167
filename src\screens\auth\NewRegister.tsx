import React, {useCallback, useState, useEffect} from 'react';
import {Linking, Platform} from 'react-native';
import {useNavigation} from '@react-navigation/core';
import axios from 'axios';

import {useData, useTheme, useTranslation} from '../../hooks/';
import * as regex from '../../constants/regex';
import {Block, Button, Input, Text, Checkbox} from '../../components/';

const isAndroid = Platform.OS === 'android';
const API_URL = process.env.EXPO_PUBLIC_SERVER_URI;
interface IRegister {
  first_name: string;
  last_name: string;
  phone_number: string;
  email: string;
  agreed: boolean;
}

interface IRegisterValidation {
  first_name: boolean;
  last_name: boolean;
  phone_number: boolean;
  email: boolean;
  agreed: boolean;
}

const NewRegister = () => {
  const {isDark} = useData();
  const {t} = useTranslation();
  const navigation = useNavigation();
  const [isValid, setIsValid] = useState<IRegisterValidation>({
    first_name: false,
    last_name: false,
    phone_number: false,
    email: false,
    agreed: false,
  });
  const [registration, setRegistration] = useState<IRegister>({
    first_name: '',
    last_name: '',
    phone_number: '',
    email: '',
    agreed: false,
  });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const {colors, gradients, sizes} = useTheme();

  // Validate fields whenever registration data changes
  useEffect(() => {
    setIsValid({
      first_name: regex.name.test(registration.first_name),
      last_name: regex.name.test(registration.last_name),
      phone_number: regex.phone.test(registration.phone_number),
      email: regex.email.test(registration.email),
      agreed: registration.agreed,
    });
  }, [registration]);

  const handleChange = useCallback(
    (value: Partial<IRegister>) => {
      setRegistration((state) => ({...state, ...value}));
    },
    [],
  );

  const handleSignUp = useCallback(async () => {
    if (Object.values(isValid).includes(false)) {
      setError('Please fill all fields correctly before submitting.');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const url = `${API_URL}/auth/token/register/`
      const response = await axios.post(url, registration);
      
      if (response.status === 200 || response.status === 201) {
        // Registration successful, navigate to VerifyToken screen
        navigation.navigate('VerifyToken', {
          email: registration.email,
          isSignUp: true,
        });
      }
    } catch (error: any) {
      // Handle specific API error messages if available
      // Handle specific API error messages if available
      if (error.response?.data?.error) {
        setError(error.response.data.error);
      } else {
        setError('Registration failed. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  }, [registration, navigation, isValid]);

  const isFormValid = !Object.values(isValid).includes(false) && 
    registration.first_name !== '' && 
    registration.last_name !== '' && 
    registration.phone_number !== '' && 
    registration.email !== '';

  return (
    <Block safe marginTop={sizes.md}>
      <Block paddingHorizontal={sizes.s}>
        <Block
          keyboard
          behavior={!isAndroid ? 'padding' : 'height'}
          marginTop={sizes.xl}
          marginBottom={sizes.xxl}>
          <Block
            flex={0}
            radius={sizes.sm}
            marginHorizontal="3%"
            marginTop="10%"
            shadow={!isAndroid}>
            <Block
              blur
              flex={0}
              intensity={90}
              radius={sizes.sm}
              overflow="hidden"
              justify="space-evenly"
              tint={colors.blurTint}
              paddingVertical={sizes.sm}>
              <Text h3 center bold>
                {t('common.signup')}
              </Text>
              <Block paddingHorizontal={sizes.sm}>
                <Input
                  autoCapitalize="none"
                  marginBottom={sizes.m}
                  label={t('common.firstName')}
                  placeholder={t('common.firstNamePlaceholder')}
                  success={Boolean(
                    registration.first_name && isValid.first_name,
                  )}
                  danger={Boolean(
                    registration.first_name && !isValid.first_name,
                  )}
                  onChangeText={(value) => handleChange({first_name: value})}
                />
                <Input
                  autoCapitalize="none"
                  marginBottom={sizes.m}
                  label={t('common.lastName')}
                  placeholder={t('common.lastNamePlaceholder')}
                  success={Boolean(registration.last_name && isValid.last_name)}
                  danger={Boolean(registration.last_name && !isValid.last_name)}
                  onChangeText={(value) => handleChange({last_name: value})}
                />
                <Input
                  autoCapitalize="none"
                  marginBottom={sizes.m}
                  label={t('common.phoneNumber')}
                  placeholder={t('common.phoneNumberPlaceholder')}
                  success={Boolean(
                    registration.phone_number && isValid.phone_number,
                  )}
                  danger={Boolean(
                    registration.phone_number && !isValid.phone_number,
                  )}
                  onChangeText={(value) => handleChange({phone_number: value})}
                />
                <Input
                  autoCapitalize="none"
                  marginBottom={sizes.m}
                  label={t('common.email')}
                  keyboardType="email-address"
                  placeholder={t('common.emailPlaceholder')}
                  success={Boolean(registration.email && isValid.email)}
                  danger={Boolean(registration.email && !isValid.email)}
                  onChangeText={(value) => handleChange({email: value})}
                />
                <Block row flex={0} align="center" justify="flex-start">
                  <Checkbox
                    marginRight={sizes.sm}
                    checked={registration?.agreed}
                    onPress={(value) => handleChange({agreed: value})}
                  />
                  <Text paddingRight={sizes.s}>
                    {t('common.agree')}
                    <Text
                      semibold
                      onPress={() => {
                        Linking.openURL('https://www.creative-tim.com/terms');
                      }}
                    >
                      {t('common.terms')}
                    </Text>
                  </Text>
                </Block>
              </Block>
              {error && (
                <Text color={colors.danger} center marginBottom={sizes.s}>
                  {error}
                </Text>
              )}
              <Button
                onPress={handleSignUp}
                marginVertical={sizes.s}
                marginHorizontal={sizes.sm}
                gradient={gradients.primary}
                loading={isLoading}
                disabled={!isFormValid}>
                <Text bold white transform="uppercase">
                  {t('common.signup')}
                </Text>
              </Button>

              <Block
                row
                center
                align="center"
                marginVertical={sizes.s}
                marginHorizontal={sizes.sm}>
                <Text>Already have an account? </Text>
                <Text
                  bold
                  primary
                  transform="uppercase"
                  marginLeft={1}
                  onPress={() => navigation.navigate('Login')}>
                  {t('common.signin')}
                </Text>
              </Block>
            </Block>
          </Block>
        </Block>
      </Block>
    </Block>
  );
};

export default NewRegister;