import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const BackButton: React.FC = () => {
  const navigation = useNavigation();

  return (
    <TouchableOpacity onPress={() => navigation.goBack()} style={{ paddingHorizontal: 15 }}>
      <Feather name="arrow-left" size={24} color="#000" />
    </TouchableOpacity>
  );
};

export default BackButton;
