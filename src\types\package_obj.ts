import {IPackagesPayload, IServicesPayload} from '.';

// Package option chosen by the user
interface PackageOption {
  id: number;
  time: number;
  price: string;
  package: IPackagesPayload;
}

// Individual reward entry
interface Reward {
  id: number;
  points: number;
  reward_date: string;
  created_at: string;
}

// User's current reward balance
interface RewardBalance {
  total_points: number;
  last_updated: string;
}

// User information
interface User {
  id: number;
  email: string;
  phone_number: string;
  name: string;
  first_name: string;
  last_name: string;
  gender: 'male' | 'female' | string;
  role: 'customer' | 'admin' | string;
  address: string | null;
  date_of_birth: string | null;
  profile_picture: string | null;
  medical_issues: string | null;
  goal: string | null;
  rewards: Reward[];
  reward_balance: RewardBalance;
}

// Main subscription type
export interface Package_Obj {
  id: number;
  user: User;
  total_time: number;
  remaining_time: number;
  package_option: PackageOption;
  time_deducted: number;
  active: boolean;
  expiry_date: string;
  created_at: string;
  updated_at: string;
}
