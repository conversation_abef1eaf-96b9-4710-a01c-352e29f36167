import 'react-native-gesture-handler';
import React from 'react';
import {DataProvider} from './src/hooks';
import AppNavigation from './src/navigation/App';
import Toast from 'react-native-toast-message';
import {NewAuthProvider} from './src/context/AuthContext';
export default function App() {
  return (
    <NewAuthProvider>
      <DataProvider>
          <AppNavigation />
          <Toast />
      </DataProvider>
    </NewAuthProvider>
  );
}
