import React, {useState} from 'react';
import {useTheme, useTranslation} from '../hooks/';
import {Block, Button, Image, Text} from '../components/';
import {ActivityIndicator, Platform} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {Feather} from '@expo/vector-icons';
import useGetActivePackage from '../hooks/useGetActivePackage';
import ActivePackageCard from '../components/ActivePackageCard';
import Constants from 'expo-constants';
interface Location {
  id: number;
  name: string;
  address: string;
  image: any;
  location: string;
}

const UserActivePackage = () => {
  const {t} = useTranslation();
  const [tab, setTab] = useState<number>(0);
  const {isLoading, activePackage} = useGetActivePackage();

  const {assets, colors, sizes} = useTheme();
  const locations: Location[] = [
    {
      id: 1,
      name: 'Studio Al Warqa Mall',
      address: 'Al Warqa Mall, Tripoli st, Dubai',
      location: 'A',
      image: assets.gym1,
    },
    {
      id: 2,
      name: 'Studio Al mizhar branch',
      address: 'Al mizhar branch (inside fithub), Dubai',
      location: 'B',
      image: assets.gym2,
    },
  ];
  const navigation = useNavigation();

  return (
    <Block
      safe
      style={{
        paddingTop:
          Platform.OS === 'android' ? Constants.statusBarHeight + 30 : 30,
      }}>
      <Block color={colors.card} flex={0} padding={sizes.padding}>
        <Text center h3 font={'bold'}>
          {/* {t('home.bookings')} */}
          Active Package
        </Text>
      </Block>

      {/* products list */}
      <Block
        scroll
        paddingHorizontal={sizes.padding}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: sizes.l}}>
        <Block row wrap="wrap" justify="space-between" marginTop={sizes.sm}>
          {isLoading ? (
            <Block card padding={sizes.sm} center flex={1}>
              <ActivityIndicator size="large" color={colors.primary} />
            </Block>
          ) : activePackage ? (
            <Block>
              <ActivePackageCard
                id={activePackage.id}
                total_time={activePackage?.total_time}
                remaining_time={activePackage?.remaining_time}
                time_deducted={activePackage?.time_deducted}
                package_option={activePackage?.package_option}
                expiry_date={activePackage.expiry_date}
                active={activePackage.active}
              />

              <Block padding={sizes.sm} align="center" flex={0}>
                <Button
                  primary
                  marginTop={sizes.sm}
                  width={'100%'}
                  paddingHorizontal={20}
                  row
                  align="center"
                  justify="center"
                  onPress={() => navigation.navigate('Bookings' as never)}>
                  <Feather name="calendar" size={20} color={colors.white} />
                  <Text white marginLeft={sizes.s}>
                    Use Remaining Time
                  </Text>
                </Button>
              </Block>
            </Block>
          ) : (
            <Block padding={sizes.sm} align="center">
              <Text p color={colors.gray}>
                There is no active package to display now...
              </Text>

              <Button
                primary
                marginTop={sizes.sm}
                flex={1.2}
                paddingHorizontal={20}
                row
                align="center"
                justify="center"
                onPress={() => navigation.navigate('Bookings' as never)}>
                <Feather name="calendar" size={20} color={colors.white} />
                <Text white marginLeft={sizes.s}>
                  Book Appointment
                </Text>
              </Button>
            </Block>
          )}
        </Block>
      </Block>
    </Block>
  );
};

export default UserActivePackage;
