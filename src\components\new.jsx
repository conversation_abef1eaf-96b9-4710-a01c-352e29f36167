import React from 'react';
import {TouchableOpacity} from 'react-native';
import {Feather} from '@expo/vector-icons';

import Block from './Block';
import Text from './Text';
import {IAppointment} from '../constants/types';
import {useTheme, useTranslation} from '../hooks/';

const AppointmentCard = ({
  client_name,
  therapist_name,
  appointment_date,
  service_name,
  service_duration,
  location_name,
  notes,
}: IAppointment) => {
  const {t} = useTranslation();
  const {colors, sizes} = useTheme();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    });
  };

  return (
    <Block
      card
      flex={0}
      padding={sizes.m}
      marginBottom={sizes.sm}
      width={sizes.width - sizes.padding * 2}
      style={{
        shadowColor: colors.black,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
      }}>
      <Block row align="center" marginBottom={sizes.sm}>
        <Block
          flex={0}
          width={48}
          height={48}
          radius={24}
          align="center"
          primary
          justify="center"
          marginRight={sizes.sm}>
          <Text color={colors.white} bold size={24} paddingTop={9}>
            {client_name.charAt(0)}
          </Text>
        </Block>
        <Block>
          <Text h5 semibold>
            {client_name}
          </Text>
        </Block>
      </Block>

      <Block row marginBottom={sizes.sm}>
        <Block flex={0} marginRight={sizes.m}>
          <Feather name="calendar" size={20} color={colors.primary} />
        </Block>
        <Text p semibold>
          {formatDate(appointment_date)}
        </Text>
      </Block>

      <Block row marginBottom={sizes.sm}>
        <Block flex={0} marginRight={sizes.m}>
          <Feather name="activity" size={20} color={colors.primary} />
        </Block>
        <Text p>
          {service_name} ({service_duration} min)
        </Text>
      </Block>

      <Block row marginBottom={sizes.sm}>
        <Block flex={0} marginRight={sizes.m}>
          <Feather name="map-pin" size={20} color={colors.primary} />
        </Block>
        <Text p>{location_name}</Text>
      </Block>

      {notes && (
        <Block card padding={sizes.sm} color={colors.card}>
          <Text h5 color={colors.text}>
            {t('common.extraNotes')}:
          </Text>
          <Text p color={colors.text} marginTop={9}>
            {notes}
          </Text>
        </Block>
      )}
{/* 
      <TouchableOpacity>
        <Block
          row
          flex={0}
          align="center"
          justify="center"
          marginTop={sizes.sm}
          padding={sizes.s}
          radius={sizes.buttonRadius}
          primary>
          <Text p color={colors.white} semibold marginRight={sizes.s}>
            {t('common.viewDetails')}
          </Text>
          <Feather name="chevron-right" size={20} color={colors.white} />
        </Block>
      </TouchableOpacity> */}
    </Block>
  );
};

export default AppointmentCard;
