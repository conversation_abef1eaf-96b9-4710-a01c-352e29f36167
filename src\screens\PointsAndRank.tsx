import React, {useRef, useState} from 'react';
import {FlatList, Platform, ScrollView, StyleSheet, View} from 'react-native';
import {Feather, FontAwesome6} from '@expo/vector-icons';

import {useTheme} from '../hooks/';
import {Block, Button, Text} from '../components';
import {IRewards, useAuth} from '../context/AuthContext';
import {useNavigation} from '@react-navigation/native';
import {format} from 'date-fns';
import Constants from 'expo-constants';
export default function RewardsScreen() {
  const {assets, colors, gradients, sizes} = useTheme();
  const authData = useAuth();
  const userData = authData?.userData;
  const navigation = useNavigation();
  const [activeIndex, setActiveIndex] = useState(1); // Start with the second item as active

  const currentPoints = userData?.reward_balance?.total_points || 0;
  const maxPoints = 10000;
  const progress = Math.min((currentPoints / maxPoints) * 100, 100);
  const remainingPoints = Math.max(maxPoints - currentPoints, 0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const renderCommissionItem = ({item}: {item: IRewards}) => (
    <Block card marginBottom={8} padding={sizes.sm}>
      <Block row justify="space-between" marginBottom={sizes.xs}>
        <Text p semibold>
          {item.points} - <Text size={13}>Reward Pts</Text>
        </Text>

        {/* <Block row justify="space-between"> */}
        <Text p color={colors.gray}>
          {format(new Date(item.reward_date), 'MMM dd, yyyy')}
        </Text>
        {/* </Block> */}
      </Block>
    </Block>
  );

  return (
    <Block safe marginBottom={120} style={{
      paddingTop:
        Platform.OS === 'android' ? Constants.statusBarHeight + 30 : 30,
    }}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Block>
          {/* Header */}
          {/* Header */}
          <Block
            row
            justify="space-between"
            marginBottom={sizes.s}
            paddingHorizontal={sizes.base}
            paddingVertical={60}
            gradient={['#966419', '#cb964a']}>
            <Block>
              <Text h3 white>
                Hi {userData?.first_name ?? 'User'}
              </Text>
              <Block row align="center" marginTop={sizes.xs}>
                <Block radius={10} padding={sizes.xs} flex={1.5}>
                  <Text h4 bold white>
                    {currentPoints} Points
                  </Text>
                </Block>
              </Block>
            </Block>
          </Block>

          <Block
            card
            margin={sizes.sm}
            marginTop={-sizes.xl}
            style={styles.statsCard}>
            <Text p style={{color: '#555'}}>
              Road to{' '}
              <Text primary bold p>
                {maxPoints}
              </Text>{' '}
              Points
            </Text>
            <Block style={styles.progressContainer} marginTop={20}>
              <View style={styles.iconContainer}>
                <View
                  style={[
                    styles.iconInnerContainer,
                    {
                      backgroundColor:
                        currentPoints < maxPoints ? '#333' : '#999',
                    },
                  ]}>
                  {/* <Feather name="gift" size={20} color="white" /> */}
                  <FontAwesome6
                    name="hourglass-start"
                    size={20}
                    color="white"
                  />
                </View>
                {/* <Text style={styles.pointsText}>{currentPoints}</Text> */}
              </View>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progress,
                    {width: `${progress}%`, backgroundColor: colors.primary},
                  ]}
                />
              </View>
              <View
                style={[
                  styles.iconInnerContainer2,
                  {
                    backgroundColor:
                      currentPoints >= maxPoints ? '#333' : '#999',
                  },
                ]}>
                <Feather name="gift" size={20} color="white" />
                {/* <Text style={styles.pointsText}>{maxPoints}</Text> */}
              </View>
            </Block>

            <Block>
              {currentPoints >= maxPoints ? (
                <Text center>
                  Congrats!! 🍾🍾 , You can now enjoy
                  <Text h5 primary semibold>
                    15% off
                  </Text>{' '}
                  for every Appointments for the next 6months
                </Text>
              ) : (
                <Text center>
                  You need{' '}
                  <Text h5 primary semibold>
                    {maxPoints - currentPoints}
                  </Text>{' '}
                  more points to get{' '}
                  <Text h5 primary semibold>
                    15% off
                  </Text>{' '}
                  for every Appointments in the next 6months
                </Text>
              )}
            </Block>
          </Block>

          <Block paddingHorizontal={20} marginVertical={20}>
            <Button
              primary
              flex={1.2}
              row
              align="center"
              justify="center"
              onPress={() => navigation.navigate('Bookings')}>
              <Feather name="calendar" size={24} color={colors.white} />
              <Text
                white
                size={18}
                marginLeft={sizes.s}
                semibold
                transform="uppercase">
                Earn more Points
              </Text>
            </Button>
          </Block>

          <Block padding={sizes.sm}>
            <Text h5 semibold marginBottom={sizes.sm}>
              Recent Rewards
            </Text>
            <Block>
              <FlatList
                data={
                  userData?.rewards?.slice(
                    (currentPage - 1) * itemsPerPage,
                    currentPage * itemsPerPage,
                  ) as unknown as IRewards[]
                }
                renderItem={renderCommissionItem}
                keyExtractor={(item) => item.id.toString()}
                showsVerticalScrollIndicator={false}
                scrollEnabled={false}
                ListEmptyComponent={
                  <Block card padding={sizes.sm} align="center">
                    <Text p color={colors.gray}>
                      No rewards to display
                    </Text>
                  </Block>
                }
                ListFooterComponent={
                  userData?.rewards && userData?.rewards?.length > 0 ? (
                    <Block
                      row
                      justify="space-between"
                      marginTop={8}
                      align="center">
                      <Block row>
                        <Text p color={colors.gray}>
                          Showing{' '}
                          {Math.min(
                            (currentPage - 1) * itemsPerPage + 1,
                            userData?.rewards.length,
                          )}{' '}
                          -{' '}
                          {Math.min(
                            currentPage * itemsPerPage,
                            userData?.rewards.length,
                          )}{' '}
                          of {userData?.rewards.length}
                        </Text>
                      </Block>
                      <Block row flex={0}>
                        <Button
                          // card
                          marginRight={sizes.xs}
                          padding={sizes.xs}
                          disabled={currentPage === 1}
                          style={{opacity: currentPage === 1 ? 0.5 : 1}}
                          // opacity={currentPage === 1 ? 0.5 : 1}
                          onPress={() =>
                            setCurrentPage((prev) => Math.max(1, prev - 1))
                          }>
                          <Text p semibold color={colors.primary}>
                            Previous
                          </Text>
                        </Button>
                        <Button
                          padding={sizes.xs}
                          disabled={
                            currentPage * itemsPerPage >=
                            userData?.rewards.length
                          }
                          style={{
                            opacity:
                              currentPage * itemsPerPage >=
                              userData?.rewards.length
                                ? 0.5
                                : 1,
                          }}
                          onPress={() =>
                            setCurrentPage((prev) =>
                              Math.min(
                                Math.ceil(
                                  (userData?.rewards
                                    ? userData?.rewards.length
                                    : 0) / itemsPerPage,
                                ),
                                prev + 1,
                              ),
                            )
                          }>
                          <Text p semibold color={colors.primary}>
                            Next
                          </Text>
                        </Button>
                      </Block>
                    </Block>
                  ) : null
                }
              />
            </Block>
          </Block>
        </Block>
      </ScrollView>
    </Block>
  );
}

const styles = StyleSheet.create({
  statsCard: {
    marginTop: -30,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    alignItems: 'center',

    // marginHorizontal: 10,
  },
  iconInnerContainer: {
    alignItems: 'center',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    // backgroundColor: '#333',
    // padding: 10
    // marginHorizontal: 10,
  },
  iconInnerContainer2: {
    alignItems: 'center',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    // backgroundColor: '#999',
    // padding: 10
    // marginHorizontal: 10,
  },
  pointsText: {
    fontSize: 10,
    color: '#666666',
    marginTop: 4,
  },
  progressBar: {
    flex: 1,
    height: 10,
    backgroundColor: '#cfcece',
    borderRadius: 4,
    marginHorizontal: 10,
  },
  progress: {
    height: '100%',
    // backgroundColor: colors.primary,
    borderRadius: 4,
  },
  textContainer: {
    alignItems: 'center',
  },
});
