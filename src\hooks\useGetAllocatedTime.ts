import {useEffect, useMemo, useState} from 'react';
import {useBooking} from '../context/BookingContext';
import axios from 'axios';
import Toast from 'react-native-toast-message';

const url = process.env.EXPO_PUBLIC_SERVER_URI;

type TimeChoices = Record<string, number[]> | string[]; // Union type for time choices


const useGetAllocatedTime = () => {
  const [timeChoices, setTimeChoices] = useState<TimeChoices>([]);
  const [isLoading, setIsLoading] = useState(false);
  const {bookingData} = useBooking();

  // Derived states
  const isPackageSelected = !!bookingData?.package;
  const totalDuration = useMemo(
    () => bookingData.services?.reduce((sum, {time}) => sum + time, 0) || 0,
    [bookingData.services],
  );

  const hasRequiredData = useMemo(
    () => !!(bookingData.therapist && bookingData.date),
    [bookingData.therapist, bookingData.date],
  );

  useEffect(() => {
    if (!hasRequiredData) {
      // console.warn('Therapist or Date is missing');
      setTimeChoices([]);
      return;
    }

    const fetchAvailableTimes = async () => {
      const endpoint = isPackageSelected
        ? `appointment/available-times/packages/`
        : `appointment/available-times/services/`;

      const params = {
        therapist_id: bookingData.therapist?.id,
        date: bookingData.date,
        ...(isPackageSelected ? {} : {total_duration: totalDuration}),
      };
      setIsLoading(true);
      try {
        const {data} = await axios.get(`${url}/${endpoint}`, {params});
        setTimeChoices(data || []);
      } catch (error) {
        if (axios.isAxiosError(error)) {
          if (error.response?.status === 404) {
            Toast.show({
              type: 'error',
              text1: 'No available times for the selected date.',
            });
          } else {
            Toast.show({
              type: 'error',
              text1: 'Error fetching available times.',
            });
          }
        } else {
          Toast.show({
            type: 'error',
            text1: 'An unknown error occurred.',
          });
        }
        setTimeChoices([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAvailableTimes();
  }, [
    hasRequiredData,
    isPackageSelected,
    totalDuration,
    bookingData.therapist,
    bookingData.date,
  ]);

  // Memoized allocatedTimes
  const allocatedTimes = useMemo(() => {
    if (isPackageSelected) {
      if (typeof timeChoices === 'object' && !Array.isArray(timeChoices)) {
        return bookingData.time ? timeChoices[bookingData.time] || 0 : 0;
      }
      return 0;
    } else {
      return 0;
    }
  }, [timeChoices, isPackageSelected, bookingData.time]);

  return {timeChoices, allocatedTimes, isLoading};
};

export default useGetAllocatedTime;
