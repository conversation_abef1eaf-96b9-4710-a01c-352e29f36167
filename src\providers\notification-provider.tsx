import type React from 'react';

import {useState, useEffect, useRef, type ReactNode} from 'react';
import * as Notifications from 'expo-notifications';
import {useAuth} from '../context/AuthContext';
import {axiosPrivate} from '../lib/use-axios-private';
import {registerForPushNotificationsAsync} from '../lib/notifications';

// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

interface NotificationProviderProps {
  children: ReactNode;
}

const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [expoPushToken, setExpoPushToken] = useState<string>('');
  const [notification, setNotification] =
    useState<Notifications.Notification | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const notificationListener = useRef<Notifications.Subscription | null>(null);
  const responseListener = useRef<Notifications.Subscription | null>(null);

  const authData = useAuth();

  const saveUserPushNotificationToken = async (
    token: string,
  ): Promise<void> => {
    if (!token.length || !authData?.userData) {
      return;
    }

    try {
      const response = await axiosPrivate.post('/auth/save-expo-push-token/', {
        expo_push_token: token,
      });

      if (response.status !== 200) {
        throw new Error(`Failed to save token: ${response.status}`);
      }

      // console.log("Push token saved successfully")
    } catch (error) {
      console.error('Error saving push token:', error);
      setError('Failed to save push notification token');
    }
  };

  const initializePushNotifications = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const token = await registerForPushNotificationsAsync();

      if (token) {
        setExpoPushToken(token);
        await saveUserPushNotificationToken(token);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Error initializing push notifications:', errorMessage);
      setError(errorMessage);
      setExpoPushToken('');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Only initialize if user is authenticated
    if (authData?.userData) {
      initializePushNotifications();
    }

    // Set up notification listeners
    notificationListener.current =
      Notifications.addNotificationReceivedListener(
        (notification: Notifications.Notification) => {
          console.log('Notification received:', notification);
          setNotification(notification);
        },
      );

    responseListener.current =
      Notifications.addNotificationResponseReceivedListener(
        (response: Notifications.NotificationResponse) => {
          console.log('Notification response:', response);
          // Handle notification tap/interaction here
          // You can navigate to specific screens based on notification data
        },
      );

    return () => {
      if (notificationListener.current) {
        notificationListener.current.remove();
      }
      if (responseListener.current) {
        responseListener.current.remove();
      }
    };
  }, [authData?.userData]); // Re-run when auth state changes

  // Optional: Provide notification context values
  const contextValue = {
    expoPushToken,
    notification,
    isLoading,
    error,
    refreshToken: initializePushNotifications,
  };

  return <>{children}</>;
};

export default NotificationProvider;
