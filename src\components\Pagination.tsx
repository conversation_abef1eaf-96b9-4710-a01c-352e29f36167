
import { useMemo } from "react"
import { StyleSheet, Dimensions, Pressable, View } from "react-native"
import { Block, Text } from "../components/"
import { Entypo } from "@expo/vector-icons"
import { useTheme } from "../hooks"

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  loading?: boolean
}

const Pagination = ({ currentPage, totalPages, onPageChange, loading = false }: PaginationProps) => {
  const { colors, sizes } = useTheme()
  const screenWidth = Dimensions.get("window").width

  // Generate page numbers with ellipsis
  const pageNumbers = useMemo(() => {
    const pages = []

    // Handle case with few pages
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
      return pages
    }

    // Always show first page
    pages.push(1)

    // For many pages, use a more compact approach
    if (totalPages > 20) {
      // If we're near the beginning
      if (currentPage <= 3) {
        pages.push(2, 3, "...")
        pages.push(totalPages)
        return pages
      }

      // If we're near the end
      if (currentPage >= totalPages - 2) {
        pages.push("...", totalPages - 2, totalPages - 1, totalPages)
        return pages
      }

      // If we're in the middle
      pages.push("...", currentPage - 1, currentPage, currentPage + 1, "...", totalPages)
      return pages
    }

    // For medium number of pages
    if (currentPage > 3) {
      pages.push("...")
    }

    // Show a window around current page
    const start = Math.max(2, currentPage - 1)
    const end = Math.min(totalPages - 1, currentPage + 1)

    for (let i = start; i <= end; i++) {
      pages.push(i)
    }

    if (end < totalPages - 1) {
      pages.push("...")
    }

    // Always show last page if more than 1 page
    if (totalPages > 1 && !pages.includes(totalPages)) {
      pages.push(totalPages)
    }

    return pages
  }, [currentPage, totalPages])

  if (totalPages <= 1) return null

  return (
    <Block marginVertical={sizes.sm} style={styles.container}>
      <View style={styles.paginationRow}>
        {/* Previous button */}
        <Pressable
          style={({ pressed }) => [
            styles.navButton,
            currentPage === 1 && styles.disabledButton,
            pressed && styles.pressedButton,
          ]}
          disabled={currentPage === 1 || loading}
          onPress={() => onPageChange(currentPage - 1)}
        >
          <Entypo name="chevron-left" size={14} color={currentPage === 1 ? colors.gray : colors.primary} />
        </Pressable>

        {/* Page numbers */}
        {pageNumbers.map((page, index) => {
          if (page === "...") {
            return (
              <Text key={`ellipsis-${index}`} style={styles.ellipsis} color={colors.gray}>
                •••
              </Text>
            )
          }

          return (
            <Pressable
              key={`page-${index}`}
              style={({ pressed }) => [
                styles.pageButton,
                page === currentPage && styles.activePage,
                pressed && styles.pressedButton,
              ]}
              disabled={loading || page === currentPage}
              onPress={() => typeof page === "number" && onPageChange(page)}
            >
              <Text size={12} color={page === currentPage ? colors.white : colors.text}>
                {page}
              </Text>
            </Pressable>
          )
        })}

        {/* Next button */}
        <Pressable
          style={({ pressed }) => [
            styles.navButton,
            currentPage === totalPages && styles.disabledButton,
            pressed && styles.pressedButton,
          ]}
          disabled={currentPage === totalPages || loading}
          onPress={() => onPageChange(currentPage + 1)}
        >
          <Entypo name="chevron-right" size={14} color={currentPage === totalPages ? colors.gray : colors.primary} />
        </Pressable>
      </View>
    </Block>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  paginationRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    flexWrap: "wrap",
    paddingHorizontal: 4,
  },
  pageButton: {
    width: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 2,
    backgroundColor: "rgba(0,0,0,0.03)",
  },
  activePage: {
    backgroundColor: "#4CAF50",
  },
  navButton: {
    width: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 2,
    backgroundColor: "rgba(0,0,0,0.03)",
  },
  disabledButton: {
    opacity: 0.5,
  },
  pressedButton: {
    opacity: 0.7,
  },
  ellipsis: {
    paddingHorizontal: 2,
    fontSize: 10,
    lineHeight: 22,
  },
})

export default Pagination
