import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {Block, Text, Button} from '../../components';
import {useTheme} from '../../hooks';
import {Feather} from '@expo/vector-icons';

interface CancellationPolicyProps {
  onAgree: (agreed: boolean) => void;
  agreed: boolean;
}

const CancellationPolicy: React.FC<CancellationPolicyProps> = ({
  onAgree,
  agreed,
}) => {
  const {sizes, colors} = useTheme();

  return (
    <Block card padding={sizes.sm} marginHorizontal={sizes.s} marginBottom={30}>
      <Block row align="center" marginBottom={sizes.s}>
        <Feather name="info" size={20} color={colors.primary} />
        <Text h5 marginLeft={sizes.xs} color={colors.primary}>
          Cancellation Policy
        </Text>
      </Block>

      <Block marginBottom={sizes.sm}>
        <Text p size={14} color={colors.text}>
          • Free cancellation up to 24 hours before appointment
        </Text>
        <Text p size={14} color={colors.text}>
          • 50% charge for late cancellation or no-show
        </Text>
      </Block>

      <TouchableOpacity
        style={styles.checkboxContainer}
        onPress={() => onAgree(!agreed)}
        activeOpacity={0.8}>
        <Block
          flex={0}
          style={[
            styles.checkbox,
            {borderColor: agreed ? colors.primary : colors.gray},
            agreed && {backgroundColor: colors.primary},
          ]}>
          {agreed && <Feather name="check" size={14} color={colors.white} />}
        </Block>
        <Text p size={16} color={colors.text} style={styles.checkboxText}>
          I agree to the cancellation policy
        </Text>
      </TouchableOpacity>
    </Block>
  );
};

const styles = StyleSheet.create({
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxText: {
    flex: 1,
  },
});

export default CancellationPolicy;
