import React, {useCallback, useRef, useState, useEffect} from 'react';
import {
  ActivityIndicator,
  Animated,
  FlatList,
  Platform,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  View,
  AppState,
  AppStateStatus,
  RefreshControl,
} from 'react-native';
import Constants from 'expo-constants';
import {useData, useTheme, useTranslation} from '../hooks';
import {Block, Image, Text} from '../components';
import AppointmentCard from '../components/AppointmentsCard';
import {Feather} from '@expo/vector-icons';
import {useAuth} from '../context/AuthContext';
import useAppointments from '../hooks/useAppointments';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {Location} from './Bookings/types';
{
  /* <Feather name="calendar" size={20} color={colors.white} /> */
}
const MENU_ITEMS = [
  {icon: 'credit-card', label: 'Bookings', link: 'BookingHistory'},
  {icon: 'calendar', label: 'Book Now', link: 'Bookings'},
  // {icon: 'shopping-bag', label: 'My Package', link: 'MyActivePackage'},
  // {icon: 'map-pin', label: 'FAQ'},
];

const OFFERS = [
  {
    points: 7000,
    title: 'CHOOSE ANY ONE',
    tag: 'Free',
  },
  {
    points: 9000,
    title: 'CHOOSE ANY ONE',
    tag: 'FREE',
  },
  {
    points: 5000,
    title: 'CHOOSE ANY ONE',
    tag: 'Free',
  },
];

export default function UserHome() {
  const authData = useAuth();
  const userData = authData?.userData;
  const {isLoading, appointments, refetch} = useAppointments();
  const {assets, colors, sizes} = useTheme();
  const [refreshing, setRefreshing] = useState(false);
  const appState = useRef(AppState.currentState);

  // Refresh data when app comes back to foreground
  // useEffect(() => {
  //   const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
  //     if (
  //       appState.current.match(/inactive|background/) && 
  //       nextAppState === 'active'
  //     ) {
  //       console.log('App has come to the foreground - refreshing user home appointments');
  //       // refetch();
  //     }
  //     appState.current = nextAppState;
  //   });

  //   return () => {
  //     subscription.remove();
  //   };
  // }, [refetch]);

  // Refresh data when screen comes into focus
  // useFocusEffect(
  //   useCallback(() => {
  //     console.log('UserHome screen focused - refreshing appointments');
  //     refetch();
  //     return () => {};
  //   }, [refetch])
  // );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const locations: Location[] = [
    {
      id: 1,
      name: 'Studio Al Warqa Mall',
      address: 'Al Warqa Mall, Tripoli st, Dubai',
      location: 'A',
      image: assets.gym1,
    },
    {
      id: 2,
      name: 'Studio Al mizhar branch',
      address: 'Al mizhar branch (inside fithub), Dubai',
      location: 'B',
      image: assets.gym2,
    },
  ];
  const {t} = useTranslation();
  const [activeIndex, setActiveIndex] = useState(1);

  const onViewableItemsChanged = useCallback(({viewableItems}) => {
    if (viewableItems.length > 0) {
      setActiveIndex(viewableItems[0].index);
    }
  }, []);

  const navigation = useNavigation();

  const viewabilityConfig = {
    viewAreaCoveragePercentThreshold: 50,
  };


  const renderMenuItem = (item: (typeof MENU_ITEMS)[0], index: number) => (
    <TouchableOpacity
      key={`menu-item-${index}`}
      onPress={() => navigation.navigate(item?.link as never)}>
      <Block
        card
        width={(sizes.width - (sizes.base * 2) - (sizes.sm * 2)) / 2}
        height={80}
        marginBottom={sizes.sm}
        color={colors.white}
        align="center"
        justify="center"
        style={{
          shadowColor: colors.shadow,
          shadowOffset: {width: 0, height: 4},
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 2,
        }}>
        <Feather
          name={item.icon as keyof typeof Feather.glyphMap}
          size={24}
          color={colors.primary}
        />
        <Text
          p
          semibold
          marginTop={sizes.xs}
          center
          size={12}
          color={colors.black}>
          {item.label}
        </Text>
      </Block>
    </TouchableOpacity>
  );

  const listData = [
    {id: 1, image: assets.gym4},
    {id: 2, image: assets.gym3},
    {id: 3, image: assets.gym4},
  ];


  return (
    <Block
      safe
      style={{
        paddingTop:
          Platform.OS === 'android' ? Constants.statusBarHeight + 30 : 30,
      }}>
      <ScrollView 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        <Block paddingBottom={sizes.padding} paddingTop={sizes.text}>
          {/* Header */}
          <Block
            row
            justify="space-between"
            marginBottom={sizes.s}
            paddingHorizontal={sizes.base}>
            <Block>
              <Text h4>Hi {userData?.first_name ?? 'User'}</Text>
              {/* <Block row align="center" marginTop={sizes.xs}>
                <Block radius={10} padding={sizes.xs} flex={1.5}>
                  <Text p semibold>
                    {userData?.reward_balance?.total_points ?? 0} Reward Points
                  </Text>
                </Block>
              </Block> 
              */}
            </Block>
          </Block>

          {/* Grand Prize Banner */}
          {/* <FlatList
            horizontal
            style={{marginTop: 20, marginBottom: 20}}
            showsHorizontalScrollIndicator={false}
            initialScrollIndex={1}
            data={listData}
            keyExtractor={(item) => item?.id.toString()}
            renderItem={({item, index}) => (
              <Block
                card
                color={colors.primary}
                padding={0}
                marginBottom={10}
                width={sizes.width * 0.9}
                overflow="hidden"
                align="center"
                justify="center"
                height={220}
                marginHorizontal={6}>
                <Image
                  background
                  height={220}
                  width={sizes.width * 0.9}
                  source={assets.gym4}
                  resizeMode="cover"

                  // source={item.image}
                />
                <Block
                  color={colors.overlay}
                  position="absolute"
                  top={0}
                  left={0}
                  right={0}
                  bottom={0}
                />
              </Block>
            )}
            getItemLayout={(data, index) => ({
              length: sizes.width * 0.8 + sizes.sm,
              offset: sizes.width * 0.8 * index,
              index,
            })}
            onViewableItemsChanged={onViewableItemsChanged}
            viewabilityConfig={viewabilityConfig}
          /> */}

          {/* Menu Grid */}
          <Block
            row
            wrap="wrap"
            paddingHorizontal={sizes.base}
            justify="space-between"
            marginBottom={sizes.sm}>
            {MENU_ITEMS.map(renderMenuItem)}
          </Block>

          {/* next appointments */}
          {appointments?.length > 0 && (
            <Block marginTop={sizes.xs} paddingHorizontal={sizes.base}>
              <Block row justify="space-between" padding={0} align="center">
                <Text h5 bold>
                  {t('userHome.nextAppointment')}
                </Text>
                <TouchableOpacity
                  onPress={() =>
                    navigation.navigate('BookingHistory' as never)
                  }>
                  <Text primary semibold>
                    {t('userHome.seeAll')}
                  </Text>
                </TouchableOpacity>
              </Block>

              <Block
                scroll
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{paddingBottom: sizes.sm}}
                marginTop={10}>
                {isLoading ? (
                  <Block card padding={sizes.sm} center flex={1}>
                    <ActivityIndicator size="large" color={colors.primary} />
                  </Block>
                ) : appointments?.length > 0 ? (
                  <Block>
                    <Block>
                      {appointments?.slice(0, 1)?.map((appointment) => (
                        <AppointmentCard
                          // appointment={appointment}
                          client_name={`${appointment?.customer_obj?.first_name} ${appointment?.customer_obj?.last_name}`}
                          therapist_name={`${appointment?.therapist_obj?.first_name} ${appointment?.therapist_obj?.last_name}`}
                          appointment_date={appointment?.date}
                          time={appointment?.time}
                          service_name={
                            appointment?.package_balance
                              ? appointment?.package_option_obj?.package?.name
                              : appointment?.appointment_services[0]
                                  ?.service_name
                            // : 'Services'
                          }
                          service_duration={appointment?.total_duration}
                          reward_points={appointment?.reward_points}
                          location_name={
                            locations?.find(
                              (lo) => lo.location == appointment?.location,
                            )?.name ?? 'Location'
                          }
                          notes={appointment?.notes}
                          key={`card-${appointment?.id}`}
                          user="user"
                          type="home"
                        />
                      ))}
                    </Block>
                  </Block>
                ) : null}
                {/* <Block
                row
                wrap="wrap"
                justify="space-between"
                marginTop={sizes.sm}>
                {allAppointments
                  .slice(0, 1)
                  ?.map((appointment) => (
                    <AppointmentCard
                      {...appointment}
                      key={`card-${appointment?.id}`}
                      user="user"
                      type="home"
                    />
                  ))}
              </Block> */}
              </Block>
            </Block>
          )}

          {/* Featured Offers */}
          {/* <Block
            row
            justify="space-between"
            marginBottom={sizes.sm}
            paddingHorizontal={5}
            align="center">
            <Text h5 bold>
              {t('userHome.featuredService')}
            </Text>
            <TouchableOpacity>
              <Text primary semibold>
                {t('userHome.seeAll')}
              </Text>
            </TouchableOpacity>
          </Block> */}

          {/* <FlatList
            horizontal
            style={{marginBottom: 80}}
            showsHorizontalScrollIndicator={false}
            initialScrollIndex={1}
            data={OFFERS}
            keyExtractor={(item, index) => `offer-${index}`}
            renderItem={({item, index}) => (
              <TouchableOpacity>
                <Block
                  card
                  width={200}
                  align="center"
                  marginRight={sizes.sm}
                  marginLeft={index === 0 ? sizes.padding : 0}
                  color={colors.primary}>
                  <Block
                    card
                    color={colors.warning}
                    padding={sizes.xs}
                    width={60}
                    align="center"
                    justify="center"
                    marginBottom={sizes.sm}>
                    <Text p bold transform="uppercase">
                      {item.tag}
                    </Text>
                  </Block>
                  <Text h2 white center marginBottom={sizes.sm}>
                    {item.points.toLocaleString()}
                  </Text>
                  <Text h4 white center marginBottom={sizes.xs}>
                    POINTS
                  </Text>
                  <Text p white center bold>
                    {item.title}
                  </Text>
                </Block>
              </TouchableOpacity>
            )}
            getItemLayout={(data, index) => ({
              length: sizes.width * 0.8 + sizes.sm,
              offset: 170,
              index,
            })}
          /> */}
        </Block>
      </ScrollView>
    </Block>
  );
}